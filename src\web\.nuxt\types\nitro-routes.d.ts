// Generated by nitro
import type { Serialize, Simplify } from "nitropack/types";
declare module "nitropack/types" {
  type Awaited<T> = T extends PromiseLike<infer U> ? Awaited<U> : T
  interface InternalApi {
    '/api/core': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/core/index.get').default>>>>
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/core/index.post').default>>>>
    }
    '/auth/keycloak': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/routes/auth/keycloak.get').default>>>>
    }
    '/auth/logout': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/routes/auth/logout.post').default>>>>
    }
    '/__nuxt_error': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../node_modules/.pnpm/nuxt@3.17.3_@parcel+watcher_a79f837be77ab0e2d4d7d7938f785c76/node_modules/nuxt/dist/core/runtime/nitro/handlers/renderer').default>>>>
    }
    '/api/_nuxt_icon/:collection': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../node_modules/.pnpm/@nuxt+icon@1.13.0_magicast@_23101821a1a51355d9567424d10baa69/node_modules/@nuxt/icon/dist/runtime/server/api').default>>>>
    }
    '/api/_auth/session': {
      'delete': Simplify<Serialize<Awaited<ReturnType<typeof import('../../node_modules/.pnpm/nuxt-auth-utils@0.5.20_patc_7b0d61e75377a67e14def2045f8741f3/node_modules/nuxt-auth-utils/dist/runtime/server/api/session.delete').default>>>>
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../node_modules/.pnpm/nuxt-auth-utils@0.5.20_patc_7b0d61e75377a67e14def2045f8741f3/node_modules/nuxt-auth-utils/dist/runtime/server/api/session.get').default>>>>
    }
    '/__nuxt_island/**': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/#internal/nuxt/island-renderer').default>>>>
    }
  }
}
export {}