# Start development environment
Write-Host "Starting tmerge development environment..." -ForegroundColor Green
# Navigate to project root
Set-Location "$PSScriptRoot\.."

# Start Docker Compose
docker compose `
	-f docker-compose/dev/keycloak.yml `
	-f docker-compose/dev/observability.yml `
	-f docker-compose/dev/services.yml `
  up --build -d

if ($LASTEXITCODE -eq 0) {
    Write-Host "SUCCESS: Development environment started successfully!" -ForegroundColor Green
    Write-Host "Services available at:" -ForegroundColor Cyan
    Write-Host "   - Core API: http://localhost:8081" -ForegroundColor White
    Write-Host "   - ADO API:  http://localhost:8082" -ForegroundColor White
    Write-Host "   - Keycloak: http://localhost:8080" -ForegroundColor White
    Write-Host "   - Grafana:  http://localhost:5341" -ForegroundColor White
    Write-Host "The web app is not started by default. To start it, navigate to 'src/web' and run 'pnpm dev'." -ForegroundColor Yellow
} else {
    Write-Host "ERROR: Failed to start development environment" -ForegroundColor Red
	Read-Host ""
    exit 1
}

Set-Location $PSScriptRoot
Read-Host "Execution complete. Press Enter to exit..."
