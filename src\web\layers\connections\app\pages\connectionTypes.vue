<script setup lang="ts">
	import { getConnectionTypeImage, hasConnectionTypeImage } from '../utils/connection-images';

	useHead({
		title: "tmerge - Connection Types"
	});

	const { data: connectionTypes, error, pending } = await useFetch<ConnectionType[]>("/api/core?url=connectionTypes");

	// Computed properties for better data organization
	const enabledConnectionTypes = computed(() =>
		connectionTypes.value?.filter(ct => ct.isEnabled) || []
	);

	const disabledConnectionTypes = computed(() =>
		connectionTypes.value?.filter(ct => !ct.isEnabled) || []
	);

	const totalConnectionTypes = computed(() => connectionTypes.value?.length || 0);
</script>

<template>
	<UContainer class="py-8">
		<!-- Page Header -->
		<div class="mb-8">
			<div class="flex items-center gap-3 mb-4">
				<UIcon name="i-heroicons-puzzle-piece" class="w-8 h-8 text-primary-500" />
				<h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100">
					Connection Types
				</h1>
			</div>
			<p class="text-gray-600 dark:text-gray-400 text-lg">
				Available connection types for integrating with external services and tools.
			</p>
		</div>

		<!-- Loading State -->
		<div v-if="pending" class="flex justify-center items-center py-12">
			<UIcon name="i-heroicons-arrow-path" class="w-8 h-8 animate-spin text-primary-500" />
			<span class="ml-3 text-gray-600 dark:text-gray-400">Loading connection types...</span>
		</div>

		<!-- Error State -->
		<UAlert
			v-else-if="error"
			icon="i-heroicons-exclamation-triangle"
			color="error"
			variant="soft"
			title="Failed to load connection types"
			:description="error.toString()"
			class="mb-6"
		/>

		<!-- Content -->
		<div v-else-if="connectionTypes">
			<!-- Statistics Cards -->
			<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
				<UCard>
					<div class="flex items-center gap-4">
						<div class="p-3 bg-primary-100 dark:bg-primary-900/20 rounded-lg">
							<UIcon name="i-heroicons-puzzle-piece" class="w-6 h-6 text-primary-600 dark:text-primary-400" />
						</div>
						<div>
							<p class="text-2xl font-bold text-gray-900 dark:text-gray-100">{{ totalConnectionTypes }}</p>
							<p class="text-sm text-gray-600 dark:text-gray-400">Total Types</p>
						</div>
					</div>
				</UCard>

				<UCard>
					<div class="flex items-center gap-4">
						<div class="p-3 bg-green-100 dark:bg-green-900/20 rounded-lg">
							<UIcon name="i-heroicons-check-circle" class="w-6 h-6 text-green-600 dark:text-green-400" />
						</div>
						<div>
							<p class="text-2xl font-bold text-gray-900 dark:text-gray-100">{{ enabledConnectionTypes.length }}</p>
							<p class="text-sm text-gray-600 dark:text-gray-400">Enabled</p>
						</div>
					</div>
				</UCard>

				<UCard>
					<div class="flex items-center gap-4">
						<div class="p-3 bg-gray-100 dark:bg-gray-800 rounded-lg">
							<UIcon name="i-heroicons-x-circle" class="w-6 h-6 text-gray-600 dark:text-gray-400" />
						</div>
						<div>
							<p class="text-2xl font-bold text-gray-900 dark:text-gray-100">{{ disabledConnectionTypes.length }}</p>
							<p class="text-sm text-gray-600 dark:text-gray-400">Disabled</p>
						</div>
					</div>
				</UCard>
			</div>

			<!-- Enabled Connection Types -->
			<div v-if="enabledConnectionTypes.length > 0" class="mb-8">
				<div class="flex items-center gap-2 mb-6">
					<UIcon name="i-heroicons-check-circle" class="w-5 h-5 text-green-600 dark:text-green-400" />
					<h2 class="text-xl font-semibold text-gray-900 dark:text-gray-100">
						Available Connection Types
					</h2>
					<UBadge color="success" variant="soft" size="sm">{{ enabledConnectionTypes.length }}</UBadge>
				</div>

				<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
					<UCard
						v-for="connectionType in enabledConnectionTypes"
						:key="connectionType.id"
						class="hover:shadow-lg transition-shadow duration-200"
					>
						<div class="space-y-4">
							<!-- Header with image/icon and name -->
							<div class="flex items-start gap-4">
								<div class="flex-shrink-0">
									<div class="w-16 h-16 overflow-hidden bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-xl shadow-sm flex items-center justify-center p-3">
										<img
											v-if="hasConnectionTypeImage(connectionType)"
											:src="getConnectionTypeImage(connectionType).src"
											:alt="getConnectionTypeImage(connectionType).alt"
											class="w-full h-full object-contain filter drop-shadow-sm dark:brightness-90 dark:contrast-110"
											loading="lazy"
										/>
										<UIcon
											v-else
											:name="getConnectionTypeImage(connectionType).fallbackIcon || 'i-heroicons-link'"
											class="w-8 h-8 text-gray-500 dark:text-gray-400"
										/>
									</div>
								</div>

								<div class="flex-1 min-w-0">
									<h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 truncate">
										{{ connectionType.name }}
									</h3>
									<div class="flex items-center gap-2 mt-1">
										<UBadge color="primary" variant="soft" size="sm">
											{{ connectionType.serviceName }}
										</UBadge>
										<UBadge color="success" variant="soft" size="sm">
											<UIcon name="i-heroicons-check-circle" class="w-3 h-3 mr-1" />
											Enabled
										</UBadge>
									</div>
								</div>
							</div>

							<!-- Connection Type Details -->
							<div class="space-y-3 pt-4 border-t border-gray-200 dark:border-gray-700">
								<div class="flex items-center justify-between text-sm">
									<span class="text-gray-600 dark:text-gray-400">Service Name:</span>
									<span class="font-medium text-gray-900 dark:text-gray-100">{{ connectionType.serviceName }}</span>
								</div>

								<div class="flex items-center justify-between text-sm">
									<span class="text-gray-600 dark:text-gray-400">Status:</span>
									<div class="flex items-center gap-1">
										<div class="w-2 h-2 bg-green-500 rounded-full"></div>
										<span class="text-green-600 dark:text-green-400 font-medium">Active</span>
									</div>
								</div>

								<div class="flex items-center justify-between text-sm">
									<span class="text-gray-600 dark:text-gray-400">Type ID:</span>
									<code class="text-xs bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded font-mono">
										{{ connectionType.id.substring(0, 8) }}...
									</code>
								</div>
							</div>

							<!-- Action Button -->
							<div class="pt-4">
								<UButton
									:to="`/connections?type=${connectionType.id}`"
									variant="outline"
									size="sm"
									block
									icon="i-heroicons-plus"
								>
									Create Connection
								</UButton>
							</div>
						</div>
					</UCard>
				</div>
			</div>

			<!-- Disabled Connection Types -->
			<div v-if="disabledConnectionTypes.length > 0" class="mb-8">
				<div class="flex items-center gap-2 mb-6">
					<UIcon name="i-heroicons-x-circle" class="w-5 h-5 text-gray-500 dark:text-gray-400" />
					<h2 class="text-xl font-semibold text-gray-900 dark:text-gray-100">
						Disabled Connection Types
					</h2>
					<UBadge color="neutral" variant="soft" size="sm">{{ disabledConnectionTypes.length }}</UBadge>
				</div>

				<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
					<UCard
						v-for="connectionType in disabledConnectionTypes"
						:key="connectionType.id"
						class="opacity-60 hover:opacity-80 transition-opacity duration-200"
					>
						<div class="space-y-4">
							<!-- Header with image/icon and name -->
							<div class="flex items-start gap-4">
								<div class="flex-shrink-0">
									<div class="w-16 h-16 overflow-hidden bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl shadow-sm flex items-center justify-center p-3">
										<img
											v-if="hasConnectionTypeImage(connectionType)"
											:src="getConnectionTypeImage(connectionType).src"
											:alt="getConnectionTypeImage(connectionType).alt"
											class="w-full h-full object-contain filter drop-shadow-sm grayscale dark:brightness-75"
											loading="lazy"
										/>
										<UIcon
											v-else
											:name="getConnectionTypeImage(connectionType).fallbackIcon || 'i-heroicons-link'"
											class="w-8 h-8 text-gray-400 dark:text-gray-500"
										/>
									</div>
								</div>

								<div class="flex-1 min-w-0">
									<h3 class="text-lg font-semibold text-gray-700 dark:text-gray-300 truncate">
										{{ connectionType.name }}
									</h3>
									<div class="flex items-center gap-2 mt-1">
										<UBadge color="neutral" variant="soft" size="sm">
											{{ connectionType.serviceName }}
										</UBadge>
										<UBadge color="warning" variant="soft" size="sm">
											<UIcon name="i-heroicons-x-circle" class="w-3 h-3 mr-1" />
											Disabled
										</UBadge>
									</div>
								</div>
							</div>

							<!-- Connection Type Details -->
							<div class="space-y-3 pt-4 border-t border-gray-200 dark:border-gray-700">
								<div class="flex items-center justify-between text-sm">
									<span class="text-gray-500 dark:text-gray-500">Service Name:</span>
									<span class="font-medium text-gray-700 dark:text-gray-300">{{ connectionType.serviceName }}</span>
								</div>

								<div class="flex items-center justify-between text-sm">
									<span class="text-gray-500 dark:text-gray-500">Status:</span>
									<div class="flex items-center gap-1">
										<div class="w-2 h-2 bg-gray-400 rounded-full"></div>
										<span class="text-gray-500 dark:text-gray-400 font-medium">Inactive</span>
									</div>
								</div>

								<div class="flex items-center justify-between text-sm">
									<span class="text-gray-500 dark:text-gray-500">Type ID:</span>
									<code class="text-xs bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded font-mono text-gray-500">
										{{ connectionType.id.substring(0, 8) }}...
									</code>
								</div>
							</div>

							<!-- Disabled Notice -->
							<div class="pt-4">
								<div class="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
									<UIcon name="i-heroicons-information-circle" class="w-4 h-4 text-gray-500 mx-auto mb-1" />
									<p class="text-xs text-gray-500 dark:text-gray-400">
										This connection type is currently disabled
									</p>
								</div>
							</div>
						</div>
					</UCard>
				</div>
			</div>

			<!-- Empty State -->
			<div v-if="enabledConnectionTypes.length === 0 && disabledConnectionTypes.length === 0" class="text-center py-12">
				<UIcon name="i-heroicons-puzzle-piece" class="w-16 h-16 text-gray-400 mx-auto mb-4" />
				<h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
					No Connection Types Available
				</h3>
				<p class="text-gray-600 dark:text-gray-400">
					There are currently no connection types configured in the system.
				</p>
			</div>
		</div>
	</UContainer>
</template>
