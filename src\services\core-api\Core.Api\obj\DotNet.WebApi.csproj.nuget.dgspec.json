{"format": 1, "restore": {"/app/DotNet.WebApi/DotNet.WebApi.csproj": {}}, "projects": {"/app/DotNet.WebApi/DotNet.WebApi.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/app/DotNet.WebApi/DotNet.WebApi.csproj", "projectName": "DotNet.WebApi", "projectPath": "/app/DotNet.WebApi/DotNet.WebApi.csproj", "packagesPath": "/root/.nuget/packages/", "outputPath": "/app/DotNet.WebApi/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/root/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.VisualStudio.Azure.Containers.Tools.Targets": {"target": "Package", "version": "[1.19.6, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.4.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/share/dotnet/sdk/8.0.411/PortableRuntimeIdentifierGraph.json"}}}}}