using Ado.Application.Connections.Commands.CreateConnection;
using MediatR;
using Microsoft.Extensions.Logging;
using Microsoft.TeamFoundation.Core.WebApi;
using Microsoft.VisualStudio.Services.Common;
using Microsoft.VisualStudio.Services.WebApi;

namespace Ado.Application.AzureDevOpsClients.Validation.Queries;

public record ValidateAdoConnectionQuery : IRequest<IPagedList<TeamProjectReference>>
{
	public required string AdoInstanceUrl { get; init; }
	public required string Pat { get; set; }
}

public class ValidateAdoConnectionQueryHandler(ILogger<CreateConnectionCommandHandler> _logger) : IRequestHandler<ValidateAdoConnectionQuery, IPagedList<TeamProjectReference>>
{
	public async Task<IPagedList<TeamProjectReference>> Handle(ValidateAdoConnectionQuery request, CancellationToken cancellationToken)
	{
		var connection = new VssConnection(new Uri(request.AdoInstanceUrl), new VssBasicCredential(string.Empty, request.Pat));
		var projectClient = connection.GetClient<ProjectHttpClient>();

		var projects = await projectClient.GetProjects();

		_logger.LogInformation("Projects successfully fetched for URL: {AdoInstanceUrl} - Projects count: {ProjectsCount}. ADO connection validated successfully.", request.AdoInstanceUrl, projects.Count);

		return projects;
	}
}
