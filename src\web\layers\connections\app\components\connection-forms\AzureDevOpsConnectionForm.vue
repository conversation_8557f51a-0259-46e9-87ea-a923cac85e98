<script setup lang="ts">
	import { z } from "zod";

	// Props
	const props = defineProps<{
		connectionType: ConnectionType;
		disabled?: boolean;
	}>();

	// Form data interfaces
	export interface AzureDevOpsFormData {
		url: string;
		pat: string;
	}

	// API payload interface (what we send to the backend)
	export interface AzureDevOpsApiPayload {
		adoInstanceUrl: string;
		pat: string;
	}

	// Emits
	const emit = defineEmits<{
		"update:data": [data: AzureDevOpsApiPayload];
		"update:valid": [valid: boolean];
	}>();

	// Form state
	const formData = reactive<AzureDevOpsFormData>({
		url: "",
		pat: ""
	});

	// Validation schema
	const azureDevOpsSchema = z.object({
		url: z
			.string()
			.url("Please enter a valid URL")
			.refine((url) => {
				// Additional validation for Azure DevOps URLs
				const isServices = url.includes("dev.azure.com");
				const isServer = !isServices && (url.includes("tfs") || url.includes("azure"));
				return isServices || isServer;
			}, "Please enter a valid Azure DevOps URL"),
		pat: z.string().min(1, "Personal Access Token is required").min(52, "Personal Access Token appears to be invalid (too short)")
	});

	// Computed properties
	const urlPlaceholder = computed(() => {
		if (props.connectionType.name.includes("Services")) {
			return "https://dev.azure.com/your-organization";
		} else if (props.connectionType.name.includes("Server")) {
			return "https://your-server/tfs";
		}
		return "Enter your Azure DevOps URL";
	});

	const urlLabel = computed(() => {
		if (props.connectionType.name.includes("Services")) {
			return "Azure DevOps Services URL";
		} else if (props.connectionType.name.includes("Server")) {
			return "Azure DevOps Server URL";
		}
		return "Azure DevOps URL";
	});

	// Validation state
	const isValid = ref(false);
	const urlError = ref<string | undefined>(undefined);
	const patError = ref<string | undefined>(undefined);

	// Watch for form changes and validate
	watch(
		formData,
		async () => {
			// Clear previous errors
			urlError.value = undefined;
			patError.value = undefined;

			try {
				const validatedData = azureDevOpsSchema.parse(formData);
				isValid.value = true;
				// Transform to the expected API format
				const apiPayload = {
					pat: validatedData.pat,
					adoInstanceUrl: validatedData.url
				};
				emit("update:data", apiPayload);
				emit("update:valid", true);
			} catch (error: any) {
				isValid.value = false;

				// Handle validation errors
				if (error.errors) {
					error.errors.forEach((err: any) => {
						if (err.path.includes('url')) {
							urlError.value = err.message;
						} else if (err.path.includes('pat')) {
							patError.value = err.message;
						}
					});
				}

				// Still emit the raw data for debugging
				const apiPayload = {
					pat: formData.pat,
					adoInstanceUrl: formData.url
				};
				emit("update:data", apiPayload);
				emit("update:valid", false);
			}
		},
		{ deep: true }
	);

	// Expose validation function for parent component
	const validate = async (): Promise<AzureDevOpsApiPayload> => {
		try {
			const validatedData = azureDevOpsSchema.parse(formData);
			return {
				pat: validatedData.pat,
				adoInstanceUrl: validatedData.url
			};
		} catch (error) {
			throw error;
		}
	};

	// Reset function
	const reset = () => {
		Object.assign(formData, {
			url: "",
			pat: ""
		});
		// Clear validation errors
		urlError.value = undefined;
		patError.value = undefined;
	};

	// Expose functions to parent
	defineExpose({
		validate,
		reset,
		formData
	});
</script>

<template>
	<div class="space-y-4">
		<!-- URL Input -->
		<UFormField :label="urlLabel" name="ado-url" required :error="urlError">
			<UInput v-model="formData.url" :placeholder="urlPlaceholder" :disabled="disabled" class="w-full" />
		</UFormField>

		<!-- PAT Input -->
		<UFormField label="Personal Access Token" name="ado-pat" required :error="patError">
			<UInput v-model="formData.pat" type="password" placeholder="Enter your Personal Access Token" :disabled="disabled" class="w-full" />
		</UFormField>
	</div>
</template>
