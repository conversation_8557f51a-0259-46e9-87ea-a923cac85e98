export default defineEventHandler(async (event) => {
	// skip auth check for auth-related routes
	if (event.path.startsWith("/auth/")) {
		return;
	}

	const session = await getUserSession(event);

	// Redirect to auth if no session exists
	if (!session?.user) {
		if (event.path.startsWith("/api/")) {
			throw createError({
				statusCode: 401,
				message: "Unauthorized"
			});
		}
		return sendRedirect(event, "/auth/keycloak");
	}

	event.context.user = session.user;
});
