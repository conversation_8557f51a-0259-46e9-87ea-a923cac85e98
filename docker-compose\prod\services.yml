name: "tmerge-prod"

services:
  core-api:
    image: tmerge-prod/core-api
    build:
      context: ../../src/services
      dockerfile: ./core-api/Dockerfile
    depends_on:
      postgres-core:
        condition: service_healthy
      ado-api:
        condition: service_started

  ado-api:
    image: tmerge-prod/ado-api
    build:
      context: ../../src/services
      dockerfile: ./ado-api/Dockerfile
    depends_on:
      postgres-ado:
        condition: service_healthy

  postgres-core:
    image: postgres:15
    environment:
      POSTGRES_DB: coredb
      POSTGRES_USER: coreuser
      POSTGRES_PASSWORD: corepassword
      PGPORT: 5432
    volumes:
      - postgres_core_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U coreuser"]
      interval: 5s
      timeout: 5s
      retries: 5

  postgres-ado:
    image: postgres:15
    environment:
      POSTGRES_DB: adodb
      POSTGRES_USER: adouser
      POSTGRES_PASSWORD: adopassword
      PGPORT: 5432
    volumes:
      - postgres_ado_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U adouser"]
      interval: 5s
      timeout: 5s
      retries: 5

volumes:
  postgres_core_data:
  postgres_ado_data:
