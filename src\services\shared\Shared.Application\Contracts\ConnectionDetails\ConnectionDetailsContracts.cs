namespace Shared.Application.Contracts.ConnectionDetails;

/// <summary>
/// Base interface for provider-specific connection details responses
/// </summary>
public interface IProviderConnectionDetails
{

}

/// <summary>
/// Azure DevOps specific connection details (non-sensitive)
/// </summary>
public class AzureDevOpsConnectionDetails : IProviderConnectionDetails
{
	public required string AdoInstanceUrl { get; set; }
	public int Variant { get; set; }
}

/// <summary>
/// Generic provider details response for unknown or future providers
/// </summary>
public class GenericProviderConnectionDetails : IProviderConnectionDetails
{
	public Guid Id { get; set; }
	public Guid CoreConnectionId { get; set; }
	public required string Name { get; set; }
	public DateTime CreatedAt { get; set; }
	public DateTime LastUpdatedAt { get; set; }
	public Dictionary<string, object> AdditionalProperties { get; set; } = new();
}
