import { z } from 'zod';

// Connection types enum - matching the API response
export const CONNECTION_TYPES = {
	AZURE_DEVOPS_SERVICES: 'Azure DevOps Services',
	AZURE_DEVOPS_SERVER: 'Azure DevOps Server 2022'
} as const;

export type ConnectionTypeValue = typeof CONNECTION_TYPES[keyof typeof CONNECTION_TYPES];

// Base form data interface (common fields)
export interface BaseConnectionFormData {
	name: string;
	description?: string;
	connectionTypeId: string;
}

// Azure DevOps specific form data
export interface AzureDevOpsFormData {
	url: string;
	pat: string;
}

// Azure DevOps API payload (what gets sent to backend)
export interface AzureDevOpsApiPayload {
	adoInstanceUrl: string;
	pat: string;
}

// Combined form data interface
export interface ConnectionFormData extends BaseConnectionFormData {
	providerData?: AzureDevOpsFormData | Record<string, any>;
}

// Base validation schema (common fields only)
export const baseConnectionFormSchema = z.object({
	name: z.string()
		.min(3, 'Connection name must be at least 3 characters')
		.max(100, 'Connection name must be less than 100 characters'),
	description: z.string()
		.max(500, 'Description must be less than 500 characters')
		.optional(),
	connectionTypeId: z.string().min(1, 'Please select a connection type')
});

export type BaseConnectionFormSchema = z.infer<typeof baseConnectionFormSchema>;

// Connection type to component mapping
export const CONNECTION_TYPE_COMPONENTS = {
	'ado': 'AzureDevOpsConnectionForm'
} as const;
