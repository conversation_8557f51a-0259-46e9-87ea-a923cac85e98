using Core.Api.Extensions;
using Core.Application;
using Core.Infrastructure;
using Serilog;
using Serilog.Sinks.Grafana.Loki;

namespace Core.Api;

internal static class Program
{
	public static void Main(string[] args)
	{
		var builder = WebApplication.CreateBuilder(args);

		Log.Logger = new LoggerConfiguration()
			.MinimumLevel.Information()
			.Enrich.FromLogContext()
			.Enrich.WithProperty("service_name", "core-api")
			.WriteTo.Console()
			//TODO: get Loki URL from env variables
			.WriteTo.GrafanaLoki("http://loki:3100", labels: [new LokiLabel { Key = "service_name", Value = "core-api" }])
			.CreateLogger();

		builder.Services.AddSerilog();

		builder.Services
			.AddApplication()
			.AddPresentation()
			.AddInfrastructure(builder.Configuration);

		var app = builder.Build();

		if (app.Environment.IsDevelopment())
		{
			app.UseSwagger();
			app.UseSwaggerUI();
		}

		app.UseSerilogRequestLogging();

		app.UseExceptionHandler();

		app.ApplyMigrations();

		app.UseHttpsRedirection();

		app.MapControllers();

		app.Run();
	}
}
