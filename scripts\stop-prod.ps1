# Stop production environment
Write-Host "Stopping tmerge production environment..." -ForegroundColor Green
# Navigate to project root
Set-Location $PSScriptRoot\..

# Stop Docker Compose
docker compose -p tmerge-prod down

if ($LASTEXITCODE -eq 0) {
	Write-Host "SUCCESS: Production environment stopped successfully!" -ForegroundColor Green
}
else {
	Write-Host "ERROR: Failed to stop production environment" -ForegroundColor Red
	Read-Host ""
	exit 1
}

Set-Location $PSScriptRoot
Read-Host "Execution complete. Press Enter to exit..."
