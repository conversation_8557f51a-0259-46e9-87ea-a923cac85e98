using Ado.Application.Common.Interfaces;
using Ado.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Ado.Infrastructure;

public static class DependencyInjection
{
	public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration) =>
		services
			.AddDatabase(configuration);

	private static IServiceCollection AddDatabase(this IServiceCollection services, IConfiguration configuration)
	{
		//TODO: get from env variable instead
		string? connectionString = configuration.GetConnectionString("Database");

		services.AddDbContext<ApplicationDbContext>(
			options =>
			{
				options
					.UseNpgsql(connectionString, npgsqlOptions =>
						npgsqlOptions.MigrationsHistoryTable(HistoryRepository.DefaultTableName, Schemas.Default))
					.UseSeeding(SeedData);
			});

		services.AddScoped<IApplicationDbContext>(provider => provider.GetRequiredService<ApplicationDbContext>());

		return services;
	}

	private readonly static Action<DbContext, bool> SeedData = (context, _) =>
	{

	};
}
