/**
 * Utility functions for connection type images
 */

export interface ConnectionTypeImage {
	src: string;
	alt: string;
	fallbackIcon?: string;
}

/**
 * Get the image configuration for a connection type
 */
export function getConnectionTypeImage(connectionType: ConnectionType): ConnectionTypeImage {
	const serviceName = connectionType.serviceName?.toLowerCase();

	// Azure DevOps (all types - Services and Server)
	if (serviceName === 'ado') {
		return {
			src: '/images/ado.png',
			alt: 'Azure DevOps',
			fallbackIcon: 'i-heroicons-code-bracket'
		};
	}

	// Default fallback for unknown connection types
	return {
		src: '',
		alt: connectionType.name || 'Unknown Connection Type',
		fallbackIcon: 'i-heroicons-link'
	};
}

/**
 * Check if a connection type has a custom image
 */
export function hasConnectionTypeImage(connectionType: ConnectionType): boolean {
	const serviceName = connectionType.serviceName?.toLowerCase();
	// Currently only ADO has a custom image
	return serviceName === 'ado';
}
