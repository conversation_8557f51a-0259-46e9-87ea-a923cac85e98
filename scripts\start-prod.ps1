# Start production environment
Write-Host "Starting tmerge production environment..." -ForegroundColor Green
# Navigate to project root
Set-Location "$PSScriptRoot\.."

# Start Docker Compose
docker compose `
	-f docker-compose/prod/keycloak.yml `
	-f docker-compose/prod/observability.yml `
	-f docker-compose/prod/services.yml `
	-f docker-compose/prod/web.yml `
	up --build -d

if ($LASTEXITCODE -eq 0) {
	Write-Host "SUCCESS: Production environment started successfully!" -ForegroundColor Green
	Write-Host "Services available at:" -ForegroundColor Cyan
	Write-Host "   - Web App:  http://tmerge.localhost" -ForegroundColor White
	Write-Host "   - Keycloak: http://keycloak.localhost" -ForegroundColor White
	Write-Host "   - Grafana:  http://grafana.localhost" -ForegroundColor White
	Write-Host "   - Traefik:  http://traefik.localhost" -ForegroundColor White
}
else {
	Write-Host "ERROR: Failed to start production environment" -ForegroundColor Red
	Read-Host ""
	exit 1
}

Set-Location $PSScriptRoot
Read-Host "Execution complete. Press Enter to exit..."
