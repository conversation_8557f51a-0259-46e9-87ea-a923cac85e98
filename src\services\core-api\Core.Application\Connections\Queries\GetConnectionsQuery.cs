using Core.Application.Common.Interfaces;
using Core.Domain.Connection;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Core.Application.Connections.Queries;

public record GetConnectionsQuery : IRequest<List<Connection>>;
public class GetConnectionsQueryHandler(IApplicationDbContext context) : IRequestHandler<GetConnectionsQuery, List<Connection>>
{
	private readonly IApplicationDbContext _context = context;

	public async Task<List<Connection>> Handle(GetConnectionsQuery request, CancellationToken cancellationToken)
	{
		var connections = await _context.Connections
			.Where(x => x.IsDeleted == false)
			.Include(x => x.ConnectionType)
			.ToListAsync(cancellationToken);

		return connections;
	}
}
