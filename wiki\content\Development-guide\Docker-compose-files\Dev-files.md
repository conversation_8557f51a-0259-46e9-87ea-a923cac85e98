# Docker compose files

This document describes the Docker Compose files used for the development environment, located in `docker-compose/dev/`. These files define the services needed to run the tmerge application in development mode.

## services.yml

This file defines the core application services and databases.

### Application Services

#### core-api
- **Purpose**: Main backend API service handling core business logic
- **Image**: Built from `src/services/core-api/Dockerfile.dev`
- **Port**: 8081 (exposed for debugging)
- **Dependencies**: postgres-core database
- **Volumes**: Hot-reload setup with source code mounted and build artifacts excluded
- **Database**: Connected to postgres-core

#### ado-api
- **Purpose**: Azure DevOps integration API service
- **Image**: Built from `src/services/ado-api/Dockerfile.dev`
- **Port**: 8082 (exposed for debugging)
- **Dependencies**: postgres-core database (shared with core-api)
- **Volumes**: Hot-reload setup with source code mounted and build artifacts excluded
- **Database**: Connected to postgres-core

### Database Services

#### postgres-core
- **Purpose**: Primary PostgreSQL database for core application data
- **Image**: postgres:15
- **Database**: coredb
- **Credentials**: coreuser/corepassword
- **Port**: 5433 (mapped from internal 5432)
- **Volume**: postgres_core_data for data persistence

#### postgres-ado
- **Purpose**: Dedicated PostgreSQL database for ADO-related data
- **Image**: postgres:15
- **Database**: adodb
- **Credentials**: adouser/adopassword
- **Port**: 5434 (mapped from internal 5432)
- **Volume**: postgres_ado_data for data persistence

## observability.yml

This file sets up the observability stack for monitoring, logging, and tracing.

### Monitoring & Visualization

#### grafana
- **Purpose**: Visualization dashboard for metrics, logs, and traces
- **Image**: grafana/grafana-oss:11.6.0
- **Port**: 5341 (mapped from internal 3000)
- **Dependencies**: prometheus, loki, tempo, otelcol
- **Volumes**: Persistent data storage and provisioning configuration
- **Access**: http://localhost:5341

#### prometheus
- **Purpose**: Metrics collection and storage system
- **Image**: prom/prometheus:v2.55.0
- **Configuration**: Custom prometheus.yml with 200h retention
- **Features**: Web UI, lifecycle management, remote write receiver
- **Volume**: prometheus_data for metrics storage

### Logging & Tracing

#### loki
- **Purpose**: Log aggregation system for centralized logging
- **Image**: grafana/loki:3.4.4
- **Volume**: loki_data for log storage
- **Integration**: Works with Grafana for log visualization

#### tempo
- **Purpose**: Distributed tracing backend for request tracing
- **Image**: grafana/tempo:2.6.1
- **Configuration**: Custom tempo.yaml configuration
- **Volume**: tempo_data for trace storage
- **Integration**: Works with Grafana for trace visualization

#### otelcol
- **Purpose**: OpenTelemetry Collector for telemetry data processing
- **Image**: otel/opentelemetry-collector-contrib:0.117.0
- **Configuration**: Custom otelcol.yaml configuration
- **Role**: Collects, processes, and exports telemetry data to backends

## keycloak.yml

This file sets up the authentication and authorization services.

### Authentication Services

#### keycloak
- **Purpose**: Identity and access management system
- **Image**: quay.io/keycloak/keycloak:24.0
- **Mode**: Development mode with auto-import
- **Port**: 8080
- **Admin Credentials**: admin/admin
- **Database**: Connected to postgres-keycloak
- **Configuration**: Imports realm configuration from realm.json
- **Access**: http://localhost:8080

#### postgres-keycloak
- **Purpose**: Dedicated PostgreSQL database for Keycloak data
- **Image**: postgres:15
- **Database**: keycloakdb
- **Credentials**: keycloak/keycloakpassword
- **Port**: Internal 5433
- **Volume**: postgres_keycloak_data for data persistence

## Usage

To start the complete development environment:

```powershell
.\scripts\start-dev.ps1
```

This will start all services defined in these three compose files with the project name `tmerge-dev`.

