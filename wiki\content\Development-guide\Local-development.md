# Local Development Environment

This guide explains how to set up and start the local development environment for the tmerge application.

## Quick Start

The fastest way to get started is using the provided PowerShell scripts:

### 1. Start Development Environment
```powershell
.\scripts\start-dev.ps1
```

This script will:
- Start all required Docker containers (APIs, databases, observability stack, Keycloak)
- Build and run services in development mode with hot-reload
- Display service endpoints when successfully started

### 2. Start Web Application (Optional)
The web application runs separately and is not included in the Docker setup by default:

```powershell
cd src\web
pnpm dev
```

### 3. Stop Development Environment
```powershell
.\scripts\stop-dev.ps1
```

## Detailed Setup Instructions

### Prerequisites Check
Before starting, ensure you have installed all dependencies listed in [Prerequisites.md](Prerequisites.md).

### Step-by-Step Setup

#### 1. Ensure Docker is Running
Make sure Docker Desktop is running on your machine.

#### 2. Start Backend Services
From the project root directory, run:

```powershell
.\scripts\start-dev.ps1
```

**What this does:**
- Starts PostgreSQL databases (Core DB on port 5433, ADO DB on port 5434)
- Starts Keycloak identity server on port 8080
- Starts observability stack (Grafana, Prometheus, Loki, Tempo, OpenTelemetry Collector)
- Builds and starts Core API on port 8081
- Builds and starts ADO API on port 8082
- Configures hot-reload for all .NET services

#### 3. Start Frontend (Optional)
If you want to work on the web application:

```powershell
cd src\web
pnpm install  # Only needed on first run or when dependencies change
pnpm dev
```

The web application will be available at http://localhost:3000

> If you don't know what credentials to use for the initial login, look into the `~/src/keycloak/realm.json` file which contains the initial admin and user credentials.

## Service Endpoints

Once the development environment is running, the following services will be available:

| Service | URL | Description |
|---------|-----|-------------|
| **Core API** | http://localhost:8081 | Main application API |
| **ADO API** | http://localhost:8082 | Azure DevOps integration API |
| **Web App** | http://localhost:3000 | Frontend application (when running `pnpm dev`) |
| **Keycloak** | http://localhost:8080 | Identity and access management |
| **Grafana** | http://localhost:5341 | Monitoring and observability dashboards |

### API Documentation
- Core API Swagger: http://localhost:8081/swagger
- ADO API Swagger: http://localhost:8082/swagger

## Development Workflow

### Making Changes

#### Backend (.NET APIs)
1. Make changes to any .NET code in `src/services/`
2. The containerized services will automatically detect changes and reload
3. Check the console output for compilation status
4. APIs support remote debugging from Visual Studio or VS Code

#### Frontend (Nuxt/Vue)
1. Make changes to any code in `src/web/`
2. The Nuxt development server will hot-reload changes automatically
3. Check the browser console for any errors

### Database Management

#### Accessing Databases
- **Core Database**: `localhost:5433` (coredb/coreuser/corepassword)
- **ADO Database**: `localhost:5434` (adodb/adouser/adopassword)

#### Entity Framework Migrations
To add a new migration:
```powershell
.\scripts\add-ef-migration-script.ps1
```

Or manually:
```powershell
cd src\services\core-api
dotnet ef migrations add [MigrationName] --project Core.Infrastructure --startup-project Core.Api

cd src\services\ado-api  
dotnet ef migrations add [MigrationName] --project Ado.Infrastructure --startup-project Ado.Api
```

> It's recommended to use the script instead of manually calling these commands.

### Monitoring and Debugging

#### Logs and Metrics
- **Grafana**: http://localhost:5341 - Pre-configured dashboards for application metrics, logs, and traces
- **Application Logs**: Streamed to console and Grafana Loki
- **Metrics**: Collected by Prometheus and visualized in Grafana
- **Distributed Tracing**: Available in Grafana Tempo

#### Debugging
- Remote debugging is enabled for all .NET containers on their respective ports
- Attach debugger from Visual Studio or VS Code
- Web application debugging through browser developer tools

## Manual Docker Commands

If you prefer to use Docker Compose directly instead of the PowerShell scripts:

### Start Development Environment
```powershell
docker compose -f docker-compose/dev/keycloak.yml -f docker-compose/dev/observability.yml -f docker-compose/dev/services.yml up --build -d
```

### Stop Development Environment
```powershell
docker compose -p tmerge-dev down
```

### View Logs
```powershell
# All services
docker compose -p tmerge-dev logs -f

# Specific service
docker compose -p tmerge-dev logs -f core-api
docker compose -p tmerge-dev logs -f ado-api
```

### Rebuild Services
```powershell
docker compose -f docker-compose/dev/services.yml up --build -d core-api ado-api
```

## Troubleshooting

### Common Issues

#### Docker Build Failures
- Ensure Docker Desktop has enough memory allocated (recommended: 8GB+)
- Clear Docker cache: `docker system prune -a`
- Check if ports are already in use

#### Database Connection Issues
- Verify PostgreSQL containers are running: `docker ps`
- Check database connection strings in `appsettings.json`
- Ensure firewall isn't blocking ports 5433, 5434

#### Hot Reload Not Working
- For .NET APIs: Check that file watching is enabled in containers
- For Nuxt: Verify `pnpm dev` is running and no TypeScript errors exist

#### Port Conflicts
If any ports are already in use, you can modify the port mappings in the respective `docker-compose/dev/*.yml` files.

### Getting Help
- Check container logs: `docker compose -p tmerge-dev logs [service-name]`
- Verify all prerequisites are installed: [Prerequisites.md](Prerequisites.md)
- Restart the entire environment: `.\scripts\stop-dev.ps1` followed by `.\scripts\start-dev.ps1`

## Environment Variables

Development environment variables are pre-configured in the Docker Compose files. Key variables include:

- `ASPNETCORE_ENVIRONMENT=Development`
- `DOTNET_USE_POLLING_FILE_WATCHER=1`
- `DOTNET_WATCH_RESTART_ON_RUDE_EDIT=1`

## Next Steps

Once your development environment is running:
1. Visit the API endpoints to verify they're working
2. Check Grafana dashboards to see telemetry data
3. Set up Keycloak users/roles as needed
4. Start developing your features!

For production deployment, see the production setup guides and use `.\scripts\start-prod.ps1` instead.
