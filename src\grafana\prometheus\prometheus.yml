global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Optional: scrape metrics from your .NET apps if they expose /metrics endpoints
  # Uncomment these if you want to use both push AND pull metrics
  # - job_name: 'core-api'
  #   static_configs:
  #     - targets: ['core-api:8081']
  #   metrics_path: '/metrics'
  #   scrape_interval: 15s
  #
  # - job_name: 'ado-api'
  #   static_configs:
  #     - targets: ['ado-api:8082']
  #   metrics_path: '/metrics'
  #   scrape_interval: 15s
