<script setup lang="ts">
	import { useConnectionsStore } from "../../stores/connections";

	useHead({
		title: "tmerge - Connections"
	});

	const store = useConnectionsStore();
	await store.fetchConnections();

	const isCreateSlideoverOpen = ref(false);
</script>

<template>
	<UContainer>
		<div class="flex justify-between items-center mb-6">
			<h1 class="text-2xl font-semibold">Connections</h1>
			<CreateConnectionSlideover v-model:open="isCreateSlideoverOpen" />
		</div>

		<div v-if="store.connections && store.connections.length > 0" class="space-y-4">
			<ConnectionListItem v-for="connection in store.connections" :key="connection.id" :connection="connection" />
		</div>

		<div v-else-if="store.connections && store.connections.length === 0" class="text-center py-12">
			<div class="text-gray-500 mb-4">
				<UIcon name="i-heroicons-link" class="w-12 h-12 mx-auto mb-4" />
				<p class="text-lg">No connections found</p>
				<p class="text-sm">Create your first connection to get started</p>
			</div>
		</div>

		<div v-else class="text-center py-12">
			<p>Loading connections...</p>
		</div>
	</UContainer>
</template>
