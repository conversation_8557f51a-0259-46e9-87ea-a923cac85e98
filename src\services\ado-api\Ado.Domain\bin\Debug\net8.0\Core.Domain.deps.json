{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"Core.Domain/1.0.0": {"dependencies": {"SonarAnalyzer.CSharp": "10.6.0.109712"}, "runtime": {"Core.Domain.dll": {}}}, "SonarAnalyzer.CSharp/10.6.0.109712": {}}}, "libraries": {"Core.Domain/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "SonarAnalyzer.CSharp/10.6.0.109712": {"type": "package", "serviceable": true, "sha512": "sha512-fMwoXhlmFrcIYLpmCZOPXQkKwQD6WoFsmkZp8FZ7RJ9Db+ELUg56qh1X4Vy2nU7ZOWmuAMDOMosYxiQKNLBWtw==", "path": "sonaranalyzer.csharp/10.6.0.109712", "hashPath": "sonaranalyzer.csharp.10.6.0.109712.nupkg.sha512"}}}