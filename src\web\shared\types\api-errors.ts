/**
 * API Error Types
 *
 * Type definitions for different API error response formats
 * that can be returned from the backend services.
 */

// Base error interface
export interface ApiError {
  status?: number;
  title?: string;
  detail?: string;
  type?: string;
  instance?: string;
}

// Validation error structure from ASP.NET Core
export interface ValidationProblemDetails extends ApiError {
  status: 400;
  errors: Record<string, string[]>;
}

// Standard problem details from ASP.NET Core
export interface ProblemDetails extends ApiError {
  status: number;
  title: string;
  detail?: string;
}

// Custom error structure for API communication errors
export interface ApiCommunicationError {
  statusCode: number;
  title: string;
  detail?: any;
}

// Union type for all possible error responses
export type ApiErrorResponse =
  | ValidationProblemDetails
  | ProblemDetails
  | ApiCommunicationError
  | Error
  | any;

// Error severity levels
export type ErrorSeverity = 'error' | 'warning' | 'info';

// Processed error for UI display
export interface ProcessedError {
  title: string;
  message: string;
  severity: ErrorSeverity;
  statusCode?: number;
  fieldErrors?: Record<string, string[]>;
  originalError?: any;
}

// Error context for better error handling
export interface ErrorContext {
  operation: string;
  resource?: string;
  userId?: string;
  timestamp: Date;
}

// Type guards for error identification
export function isValidationError(error: any): error is ValidationProblemDetails {
  return error &&
         typeof error === 'object' &&
         error.status === 400 &&
         error.errors &&
         typeof error.errors === 'object';
}

export function isProblemDetails(error: any): error is ProblemDetails {
  return error &&
         typeof error === 'object' &&
         typeof error.status === 'number' &&
         typeof error.title === 'string';
}

export function isApiCommunicationError(error: any): error is ApiCommunicationError {
  return error &&
         typeof error === 'object' &&
         typeof error.statusCode === 'number' &&
         typeof error.title === 'string';
}

export function isFetchError(error: any): boolean {
  return error &&
         (error.name === 'FetchError' ||
          error.constructor?.name === 'FetchError' ||
          (error.data && typeof error.data === 'object') ||
          (typeof error.statusCode === 'number') ||
          (typeof error.status === 'number') ||
          error.response?._data);
}
