# Commands

## Starting Docker Compose with the local development setup
You can run all the containers locally in dev mode. Changes made to any of the services are being watched and will be applied immediately.

>`docker compose -f docker-compose-dev.yml up --build`

The frontend application can be started independently with yarn/npm, outside of the containers. All the API requests will be forwarded into the containers.

## Starting Docker Compose with the production setup
Starting the containers in production mode will not listen to any changes made to the source code, it will build and start the containers with the state they are in at the moment of running the command.

>`docker compose -f docker-compose.yml up --build`
