// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
	compatibilityDate: '2024-11-01',
	devtools: { enabled: true },
	vite: {
		server: {
			watch: {
				usePolling: true,
				interval: 500
			},
		},
	},
	nitro: {
		imports: {
			dirs: [
				"server/utils/**", // workaround until utils auto-import is fixed
			]
		},
		watchOptions: {
			usePolling: true,
			interval: 500,
		},
	},
	css: ["~/assets/css/main.css"],
	future: {
		compatibilityVersion: 4
	},
	modules: ["@nuxt/ui", "@pinia/nuxt", "@vueuse/nuxt", "nuxt-auth-utils"]
})
