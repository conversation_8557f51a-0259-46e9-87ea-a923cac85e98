$result = dotnet ef --help

if ($LASTEXITCODE -eq 0) {
    Write-Host "Entity Framework Core CLI is available." -ForegroundColor Green

    $title = "Select database"
    $message = "Which service's database do you want to create a migrate script for?"
    $option1 = New-Object System.Management.Automation.Host.ChoiceDescription "&Core API", "Core API"
    $option2 = New-Object System.Management.Automation.Host.ChoiceDescription "&ADO API", "ADO API"
    $options = [System.Management.Automation.Host.ChoiceDescription[]]($option1, $option2)
    $defaultChoice = 0
    $choiceResult = $Host.UI.PromptForChoice($title, $message, $options, $defaultChoice)

    $selectedValue = ""
    switch ($choiceResult) {
        0 { $selectedValue = "./src/services/core-api/Core.Infrastructure/Core.Infrastructure.csproj" }
        1 { $selectedValue = "./src/services/ado-api/Ado.Infrastructure/Ado.Infrastructure.csproj" }
    }

    if ($selectedValue) {
        Write-Host "Creating migration script for project: $selectedValue"

		# Prompt the user for a migration name
		$migrationName = Read-Host "Enter the migration name (e.g., 'InitialCreate')"

		if (-not $migrationName) {
			Write-Host "Migration name cannot be empty." -ForegroundColor Red
			exit 1
		}

		Set-Location $PSScriptRoot\..

		try {
			dotnet ef migrations add --project $selectedValue "$migrationName"
		}
		catch {
			Write-Host "ERROR: Failed to create migration script. Please ensure the project path is correct and Entity Framework Core is properly configured." -ForegroundColor Red
			Write-Host "    - project path is correct" -ForegroundColor Red
			Write-Host "    - the selected (dev) database is up and running" -ForegroundColor Red
			Write-Host "    - Entity Framework Core is properly configured" -ForegroundColor Red
		}
    }

} else {
    Write-Host "ERROR: Entity Framework Core CLI is not available. Please install it first." -ForegroundColor Red
    exit 1
}

Set-Location $PSScriptRoot

Read-Host "Execution complete. Press Enter to exit..."
