{"id": "tmerge", "realm": "tmerge", "enabled": true, "clients": [{"clientId": "nuxt-app", "name": "Nuxt Application", "description": "Client for Nuxt App authentication", "enabled": true, "publicClient": false, "redirectUris": ["http://localhost:3000/*", "http://tmerge.localhost/*"], "webOrigins": ["http://localhost:3000", "http://tmerge.localhost/*"], "directAccessGrantsEnabled": true, "standardFlowEnabled": true, "protocol": "openid-connect", "clientAuthenticatorType": "client-secret", "secret": "kc9LGZjbQ3Bf5wCXnCrpVBB6AYTXyBaK", "serviceAccountsEnabled": true, "authorizationServicesEnabled": false, "implicitFlowEnabled": false, "protocolMappers": [{"name": "client roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-client-role-mapper", "consentRequired": false, "config": {"multivalued": "true", "userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "client_roles", "jsonType.label": "String", "client.id": "nuxt-app"}}]}], "users": [{"username": "testuser", "enabled": true, "emailVerified": true, "firstName": "Test", "lastName": "User", "email": "<EMAIL>", "credentials": [{"type": "password", "value": "Test@1234", "temporary": false}], "realmRoles": ["offline_access", "default-roles-master"], "clientRoles": {"nuxt-app": ["user"]}}, {"username": "adminuser", "enabled": true, "emailVerified": true, "firstName": "Admin", "lastName": "User", "email": "<EMAIL>", "credentials": [{"type": "password", "value": "Test@1234", "temporary": false}], "realmRoles": ["offline_access", "default-roles-master"], "clientRoles": {"nuxt-app": ["user", "admin"]}}], "roles": {"realm": [{"name": "user", "description": "Basic user role"}], "client": {"nuxt-app": [{"name": "user", "description": "User role for the UI app users"}, {"name": "admin", "description": "Admin role for the UI app users"}]}}}