export type DateFormatStyle = "dd.mm.yyyy" | "mm/dd/yyyy" | "yyyy.mm.dd";

export function useDateFormatting() {
	const currentFormat = useLocalStorage<DateFormatStyle>("preferredDateFormat", "dd.mm.yyyy", {
		initOnMounted: true
	});

	const formatDate = (date: string) => {
		const [year, month, day] = getPartsOfDate(date);

		switch (currentFormat.value) {
			case "dd.mm.yyyy":
				return `${day}.${month}.${year}`;
			case "mm/dd/yyyy":
				return `${month}/${day}/${year}`;
			case "yyyy.mm.dd":
				return `${year}.${month}.${day}`;
			default:
				return `${day}.${month}.${year}`;
		}
	};

	const getPartsOfDate = (date: string) => {
		const [year, month, day] = date.substring(0, 10).split("-");
		return [year, month, day];
	}

	return {
		formatDate,
		getPartsOfDate,
		currentFormat,
		setFormat: (format: DateFormatStyle) => (currentFormat.value = format)
	};
}
