using Ado.Api.Infrastructure;
using OpenTelemetry.Metrics;
using OpenTelemetry.Resources;
using OpenTelemetry.Trace;

namespace Ado.Api;

public static class DependencyInjection
{
	private const string SERVICE_NAME = "ado-api";

	public static IServiceCollection AddPresentation(this IServiceCollection services)
	{
		services.AddSwaggerGen();

		services.AddControllers();

		services.AddExceptionHandler<GlobalExceptionHandler>();
		services.AddProblemDetails();

		//TODO: get OtelCol URL from env variables
		services.AddOpenTelemetry()
			.ConfigureResource(r => r.AddService(SERVICE_NAME))
			.WithTracing(t => t
				.AddAspNetCoreInstrumentation()
				.AddHttpClientInstrumentation()
				.AddOtlpExporter(o =>
					o.Endpoint = new("http://otelcol:4317")))
			.WithMetrics(m => m
				.AddRuntimeInstrumentation()
				.AddAspNetCoreInstrumentation()
				.AddOtlpExporter(o =>
					o.Endpoint = new("http://otelcol:4317")));

		return services;
	}
}
