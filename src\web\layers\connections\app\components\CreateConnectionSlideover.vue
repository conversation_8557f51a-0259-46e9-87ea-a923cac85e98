<script setup lang="ts">
	import type { FormSubmitEvent } from "@nuxt/ui";

	import { baseConnectionFormSchema, CONNECTION_TYPE_COMPONENTS, type BaseConnectionFormData, type BaseConnectionFormSchema, type AzureDevOpsApiPayload } from "../types/connection-form";
	import { useConnectionsStore } from "../stores/connections";
	import { getConnectionTypeImage, hasConnectionTypeImage } from "../utils/connection-images";

	// Props and emits
	const props = defineProps<{
		open: boolean;
	}>();

	const emit = defineEmits<{
		"update:open": [value: boolean];
		success: [connection: Connection];
	}>();

	// Reactive state
	const isOpen = computed({
		get: () => props.open,
		set: (value) => emit("update:open", value)
	});

	// Form state
	const formData = reactive<BaseConnectionFormData>({
		name: "",
		description: "",
		connectionTypeId: ""
	});

	// Provider-specific form data
	const providerFormData = ref<AzureDevOpsApiPayload | Record<string, any>>({});
	const isProviderFormValid = ref(false);

	const isSubmitting = ref(false);
	const store = useConnectionsStore();
	const toast = useToast();
	const form = ref();
	const providerFormRef = ref();

	// Error handling
	const { handleError, hasFieldErrors, getFieldErrors } = useApiError();
	const currentError = ref<ProcessedError | null>(null);
	const fieldErrors = ref<Record<string, string[]>>({});

	// Connection types data
	const { data: connectionTypes } = await useFetch<ConnectionType[]>("/api/core?url=connectionTypes");

	// Connection types for dropdown - use actual API data
	const connectionTypeOptions = computed(() => {
		if (!connectionTypes.value) return [];
		return connectionTypes.value.map((ct) => {
			const image = getConnectionTypeImage(ct);
			return {
				label: ct.name,
				value: ct.id,
				avatar: hasConnectionTypeImage(ct) ? { src: image.src, alt: image.alt } : undefined,
				icon: !hasConnectionTypeImage(ct) ? image.fallbackIcon : undefined
			};
		});
	});

	// Get selected connection type
	const selectedConnectionType = computed(() => {
		if (!connectionTypes.value || !formData.connectionTypeId) return null;
		return connectionTypes.value.find((ct) => ct.id === formData.connectionTypeId);
	});

	// Determine which component to show
	const providerComponent = computed(() => {
		if (!selectedConnectionType.value) return null;
		const serviceName = selectedConnectionType.value.serviceName;
		return CONNECTION_TYPE_COMPONENTS[serviceName as keyof typeof CONNECTION_TYPE_COMPONENTS] || null;
	});

	// Computed properties
	const showProviderForm = computed(() => formData.connectionTypeId !== "" && providerComponent.value);

	// Methods
	function resetForm() {
		Object.assign(formData, {
			name: "",
			description: "",
			connectionTypeId: ""
		});
		providerFormData.value = {};
		isProviderFormValid.value = false;
		providerFormRef.value?.reset?.();

		// Clear errors
		currentError.value = null;
		fieldErrors.value = {};
		store.clearError();
	}

	function handleClose() {
		resetForm();
		isOpen.value = false;
	}

	async function handleSubmit(event: FormSubmitEvent<BaseConnectionFormSchema>) {
		if (isSubmitting.value) return;

		try {
			isSubmitting.value = true;

			// Clear previous errors
			currentError.value = null;
			fieldErrors.value = {};

			// Validate provider form if it exists
			if (providerFormRef.value && !isProviderFormValid.value) {
				try {
					await providerFormRef.value.validate();
				} catch (error) {
					throw new Error("Please fill in all required provider fields correctly");
				}
			}

			// Find the connection type
			const selectedType = selectedConnectionType.value;
			if (!selectedType) {
				throw new Error("Invalid connection type selected");
			}

			// Create the connection payload
			const payload = {
				name: event.data.name,
				description: event.data.description || "",
				connectionTypeId: selectedType.id,
				payload: providerFormData.value
			};

			const connection = await store.createConnection(payload);

			toast.add({
				title: "Success",
				description: `Connection "${connection.name}" created successfully`,
				color: "success"
			});

			emit("success", connection);
			handleClose();
		} catch (error: any) {
			console.error("Failed to create connection:", error);

			// Handle the error using our new error handling system
			if (error && typeof error === "object" && error.title) {
				// This is already a ProcessedError from the store
				currentError.value = error;

				// Extract field errors if available
				if (hasFieldErrors(error.originalError)) {
					fieldErrors.value = getFieldErrors(error.originalError);
				}

				// Show toast with processed error
				toast.add({
					title: error.title,
					description: error.message,
					color: error.severity === "warning" ? "warning" : "error"
				});
			} else {
				// Handle unexpected errors
				const processedError = handleError(error, {
					context: {
						operation: "createConnection",
						resource: "connections"
					}
				});
				currentError.value = processedError;
			}
		} finally {
			isSubmitting.value = false;
		}
	}

	// Provider form event handlers
	function handleProviderDataUpdate(data: any) {
		providerFormData.value = data;
	}

	function handleProviderValidUpdate(valid: boolean) {
		isProviderFormValid.value = valid;
	}

	// Watch for connection type changes to reset provider form
	watch(
		() => formData.connectionTypeId,
		() => {
			providerFormData.value = {};
			isProviderFormValid.value = false;
			providerFormRef.value?.reset?.();
			currentError.value = null;
		}
	);
</script>

<template>
	<USlideover
		v-model:open="isOpen"
		title="Create New Connection"
		description="Connect to your Azure DevOps instance"
		:dismissible="false"
		:close="{ onClick: handleClose }"
		:ui="{ footer: 'justify-end' }"
	>
		<UButton label="New Connection" icon="i-heroicons-plus" color="primary" @click="isOpen = true" />

		<template #body>
			<div class="h-full flex flex-col justify-between">
				<UForm id="connection-form" ref="form" :schema="baseConnectionFormSchema" :state="formData" class="space-y-6" @submit="handleSubmit">
					<!-- Connection Name -->
					<UFormField label="Connection Name" name="name" required>
						<UInput v-model="formData.name" placeholder="Enter a name for this connection" :disabled="isSubmitting" class="w-full" />
						<!-- Field-specific error for name -->
						<template v-if="fieldErrors.name" #error>
							<span class="text-red-500 text-sm">{{ fieldErrors.name[0] }}</span>
						</template>
					</UFormField>

					<!-- Description -->
					<UFormField label="Description" name="description">
						<UTextarea v-model="formData.description" placeholder="Optional description for this connection" :disabled="isSubmitting" :rows="3" class="w-full" />
						<!-- Field-specific error for description -->
						<template v-if="fieldErrors.description" #error>
							<span class="text-red-500 text-sm">{{ fieldErrors.description[0] }}</span>
						</template>
					</UFormField>

					<!-- Connection Type -->
					<UFormField label="Connection Type" name="connectionTypeId" required>
						<USelectMenu
							v-model="formData.connectionTypeId"
							:items="connectionTypeOptions"
							value-key="value"
							placeholder="Select connection type"
							:disabled="isSubmitting"
							class="w-full"
						/>
						<!-- Field-specific error for connection type -->
						<template v-if="fieldErrors.connectionTypeId" #error>
							<span class="text-red-500 text-sm">{{ fieldErrors.connectionTypeId[0] }}</span>
						</template>
					</UFormField>

					<!-- Provider-specific form component -->
					<template v-if="showProviderForm">
						<div class="border-t pt-4">
							<h3 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-4">Connection Details</h3>
							<ConnectionFormsAzureDevOpsConnectionForm
								v-if="providerComponent === 'AzureDevOpsConnectionForm'"
								ref="providerFormRef"
								:connection-type="selectedConnectionType!"
								:disabled="isSubmitting"
								@update:data="handleProviderDataUpdate"
								@update:valid="handleProviderValidUpdate"
							/>
						</div>
					</template>
				</UForm>

				<!-- Error Display -->
				<UAlert
					v-if="currentError"
					:title="currentError.title"
					:description="currentError.message"
					:color="currentError.severity === 'warning' ? 'warning' : 'error'"
					variant="soft"
					@close="currentError = null"
					class="my-6"
				/>
			</div>
		</template>

		<template #footer>
			<UButton label="Cancel" color="neutral" variant="outline" @click="handleClose" :disabled="isSubmitting" />
			<UButton label="Create Connection" color="primary" :loading="isSubmitting" type="submit" @click="form?.submit()" />
		</template>
	</USlideover>
</template>
