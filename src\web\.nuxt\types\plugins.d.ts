// Generated by Nuxt'
import type { Plugin } from '#app'

type Decorate<T extends Record<string, any>> = { [K in keyof T as K extends string ? `$${K}` : never]: T[K] }

type InjectionType<A extends Plugin> = A extends {default: Plugin<infer T>} ? Decorate<T> : unknown

type NuxtAppInjections = 
  InjectionType<typeof import("../../node_modules/.pnpm/@pinia+nuxt@0.11.0_magicast_a67d88851b9ae3b0828837d8cf9ec91f/node_modules/@pinia/nuxt/dist/runtime/payload-plugin.js")> &
  InjectionType<typeof import("../../node_modules/.pnpm/nuxt@3.17.3_@parcel+watcher_a79f837be77ab0e2d4d7d7938f785c76/node_modules/nuxt/dist/app/plugins/revive-payload.client.js")> &
  InjectionType<typeof import("../../node_modules/.pnpm/nuxt@3.17.3_@parcel+watcher_a79f837be77ab0e2d4d7d7938f785c76/node_modules/nuxt/dist/head/runtime/plugins/unhead.js")> &
  InjectionType<typeof import("../../node_modules/.pnpm/nuxt@3.17.3_@parcel+watcher_a79f837be77ab0e2d4d7d7938f785c76/node_modules/nuxt/dist/pages/runtime/plugins/router.js")> &
  InjectionType<typeof import("../../node_modules/.pnpm/nuxt-auth-utils@0.5.20_patc_7b0d61e75377a67e14def2045f8741f3/node_modules/nuxt-auth-utils/dist/runtime/app/plugins/session.server.js")> &
  InjectionType<typeof import("../../node_modules/.pnpm/nuxt@3.17.3_@parcel+watcher_a79f837be77ab0e2d4d7d7938f785c76/node_modules/nuxt/dist/app/plugins/browser-devtools-timing.client.js")> &
  InjectionType<typeof import("../../node_modules/.pnpm/nuxt@3.17.3_@parcel+watcher_a79f837be77ab0e2d4d7d7938f785c76/node_modules/nuxt/dist/app/plugins/navigation-repaint.client.js")> &
  InjectionType<typeof import("../../node_modules/.pnpm/nuxt@3.17.3_@parcel+watcher_a79f837be77ab0e2d4d7d7938f785c76/node_modules/nuxt/dist/app/plugins/check-outdated-build.client.js")> &
  InjectionType<typeof import("../../node_modules/.pnpm/nuxt@3.17.3_@parcel+watcher_a79f837be77ab0e2d4d7d7938f785c76/node_modules/nuxt/dist/app/plugins/revive-payload.server.js")> &
  InjectionType<typeof import("../../node_modules/.pnpm/nuxt@3.17.3_@parcel+watcher_a79f837be77ab0e2d4d7d7938f785c76/node_modules/nuxt/dist/app/plugins/chunk-reload.client.js")> &
  InjectionType<typeof import("../../node_modules/.pnpm/@pinia+nuxt@0.11.0_magicast_a67d88851b9ae3b0828837d8cf9ec91f/node_modules/@pinia/nuxt/dist/runtime/plugin.vue3.js")> &
  InjectionType<typeof import("../../node_modules/.pnpm/nuxt@3.17.3_@parcel+watcher_a79f837be77ab0e2d4d7d7938f785c76/node_modules/nuxt/dist/pages/runtime/plugins/prefetch.client.js")> &
  InjectionType<typeof import("../../node_modules/.pnpm/nuxt@3.17.3_@parcel+watcher_a79f837be77ab0e2d4d7d7938f785c76/node_modules/nuxt/dist/pages/runtime/plugins/check-if-page-unused.js")> &
  InjectionType<typeof import("../../node_modules/.pnpm/@nuxt+devtools@2.4.1_vite@6_1d85cc3f204fd1b0734164c2788bcaae/node_modules/@nuxt/devtools/dist/runtime/plugins/devtools.server.js")> &
  InjectionType<typeof import("../../node_modules/.pnpm/@nuxt+devtools@2.4.1_vite@6_1d85cc3f204fd1b0734164c2788bcaae/node_modules/@nuxt/devtools/dist/runtime/plugins/devtools.client.js")> &
  InjectionType<typeof import("../../node_modules/.pnpm/nuxt-auth-utils@0.5.20_patc_7b0d61e75377a67e14def2045f8741f3/node_modules/nuxt-auth-utils/dist/runtime/app/plugins/session.client.js")> &
  InjectionType<typeof import("../../node_modules/.pnpm/@nuxt+ui@3.1.2_@babel+parse_f07273e8df9b8a2f3a26358e2de67989/node_modules/@nuxt/ui/dist/runtime/plugins/colors.js")> &
  InjectionType<typeof import("../../node_modules/.pnpm/@nuxtjs+color-mode@3.5.2_magicast@0.3.5/node_modules/@nuxtjs/color-mode/dist/runtime/plugin.server.js")> &
  InjectionType<typeof import("../../node_modules/.pnpm/@nuxtjs+color-mode@3.5.2_magicast@0.3.5/node_modules/@nuxtjs/color-mode/dist/runtime/plugin.client.js")> &
  InjectionType<typeof import("../../node_modules/.pnpm/@nuxt+icon@1.13.0_magicast@_23101821a1a51355d9567424d10baa69/node_modules/@nuxt/icon/dist/runtime/plugin.js")> &
  InjectionType<typeof import("../../node_modules/.pnpm/nuxt@3.17.3_@parcel+watcher_a79f837be77ab0e2d4d7d7938f785c76/node_modules/nuxt/dist/app/plugins/dev-server-logs.js")> &
  InjectionType<typeof import("../../node_modules/.pnpm/nuxt@3.17.3_@parcel+watcher_a79f837be77ab0e2d4d7d7938f785c76/node_modules/nuxt/dist/app/plugins/check-if-layout-used.js")>

declare module '#app' {
  interface NuxtApp extends NuxtAppInjections { }

  interface NuxtAppLiterals {
    pluginName: 'vue-devtools-client' | 'nuxt:revive-payload:client' | 'nuxt:head' | 'nuxt:router' | 'session-fetch-plugin' | 'nuxt:browser-devtools-timing' | 'nuxt:revive-payload:server' | 'nuxt:chunk-reload' | 'pinia' | 'nuxt:global-components' | 'nuxt:prefetch' | 'nuxt:checkIfPageUnused' | '@nuxt/icon' | 'nuxt:checkIfLayoutUsed'
  }
}

declare module 'vue' {
  interface ComponentCustomProperties extends NuxtAppInjections { }
}

export { }
