using FluentValidation;

namespace Ado.Application.Connections.Commands.CreateConnection;
public class CreateConnectionCommandValidator : AbstractValidator<CreateConnectionCommand>
{
	public CreateConnectionCommandValidator()
	{
		RuleFor(c => c.Name).NotEmpty().MinimumLength(3);
		RuleFor(c => c.ConnectionTypeId).NotEmpty();
		RuleFor(c => c.CoreConnectionId).NotEmpty();

		RuleFor(c => c.Payload).NotNull().WithMessage("ALM-specific payload is required for creating a connection.");
		RuleFor(c => c.Payload.Pat).NotEmpty().WithMessage("Personal Access Token (PAT) is required.");
		RuleFor(c => c.Payload.AdoInstanceUrl).Must(uri => Uri.TryCreate(uri, UriKind.Absolute, out _)).WithMessage("ADO Instance URL is required in a correct format (i.e. 'https://dev.azure.com/organization'.");
	}
}
