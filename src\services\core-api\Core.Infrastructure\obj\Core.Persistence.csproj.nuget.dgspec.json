{"format": 1, "restore": {"C:\\Work\\tme\\tmerge\\src\\services\\core-api\\Core.Persistence\\Core.Persistence.csproj": {}}, "projects": {"C:\\Work\\tme\\tmerge\\src\\services\\core-api\\Core.Domain\\Core.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Work\\tme\\tmerge\\src\\services\\core-api\\Core.Domain\\Core.Domain.csproj", "projectName": "Core.Domain", "projectPath": "C:\\Work\\tme\\tmerge\\src\\services\\core-api\\Core.Domain\\Core.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Work\\tme\\tmerge\\src\\services\\core-api\\Core.Domain\\obj\\", "projectStyle": "PackageReference", "centralPackageVersionsManagementEnabled": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "centralPackageVersions": {"EFCore.NamingConventions": "9.0.0", "Microsoft.AspNetCore.OpenApi": "9.0.1", "Microsoft.EntityFrameworkCore": "9.0.1", "Microsoft.EntityFrameworkCore.Tools": "9.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.1", "Newtonsoft.Json": "13.0.3", "Npgsql.EntityFrameworkCore.PostgreSQL": "9.0.3", "Serilog": "4.2.0", "Serilog.AspNetCore": "9.0.0", "Serilog.Sinks.Seq": "9.0.0", "Swashbuckle.AspNetCore": "7.2.0"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Work\\tme\\tmerge\\src\\services\\core-api\\Core.Persistence\\Core.Persistence.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Work\\tme\\tmerge\\src\\services\\core-api\\Core.Persistence\\Core.Persistence.csproj", "projectName": "Core.Persistence", "projectPath": "C:\\Work\\tme\\tmerge\\src\\services\\core-api\\Core.Persistence\\Core.Persistence.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Work\\tme\\tmerge\\src\\services\\core-api\\Core.Persistence\\obj\\", "projectStyle": "PackageReference", "centralPackageVersionsManagementEnabled": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Work\\tme\\tmerge\\src\\services\\core-api\\Core.Domain\\Core.Domain.csproj": {"projectPath": "C:\\Work\\tme\\tmerge\\src\\services\\core-api\\Core.Domain\\Core.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[9.0.3, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"EFCore.NamingConventions": "9.0.0", "Microsoft.AspNetCore.OpenApi": "9.0.1", "Microsoft.EntityFrameworkCore": "9.0.1", "Microsoft.EntityFrameworkCore.Tools": "9.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.1", "Newtonsoft.Json": "13.0.3", "Npgsql.EntityFrameworkCore.PostgreSQL": "9.0.3", "Serilog": "4.2.0", "Serilog.AspNetCore": "9.0.0", "Serilog.Sinks.Seq": "9.0.0", "Swashbuckle.AspNetCore": "7.2.0"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}