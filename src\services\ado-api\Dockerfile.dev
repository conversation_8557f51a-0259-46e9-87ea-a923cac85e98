FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /app
EXPOSE 8082

# Install the remote debugger
RUN curl -sSL https://aka.ms/getvsdbgsh | bash /dev/stdin -v latest -l /usr/share/dotnet/vsdbg \
	&& chmod +x /usr/share/dotnet/vsdbg/vsdbg

# Copy the props files from one level above
COPY Directory.Build.props Directory.Packages.props ./

# Copy the project files and restore dependencies
COPY ado-api/Ado.Api/Ado.Api.csproj ./ado-api/Ado.Api/Ado.Api.csproj

WORKDIR /app/ado-api/Ado.Api
RUN dotnet restore

# Copy the remaining project files

WORKDIR /app
COPY ado-api ./ado-api

# Dev env variables
ENV ASPNETCORE_ENVIRONMENT=Development
ENV DOTNET_USE_POLLING_FILE_WATCHER=1
ENV DOTNET_WATCH_RESTART_ON_RUDE_EDIT=1

# Start the application in watch mode with debugging enabled
CMD ["dotnet", "watch", "run", "--project", "ado-api/Ado.Api/Ado.Api.csproj", "--urls", "http://*:8082"]
