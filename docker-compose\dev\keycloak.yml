name: "tmerge-dev"

services:
  keycloak:
    image: quay.io/keycloak/keycloak:24.0
    command: ["start-dev", "--import-realm"]
    environment:
      KC_DB: postgres
      KC_DB_URL_HOST: postgres-keycloak
      KC_DB_URL_PORT: 5433
      KC_DB_URL_DATABASE: keycloakdb
      KC_DB_USERNAME: keycloak
      KC_DB_PASSWORD: keycloakpassword
      KC_IMPORT: /opt/keycloak/data/import/realm.json
      KEYCLOAK_ADMIN: admin
      KEYCLOAK_ADMIN_PASSWORD: admin
    ports:
      - "8080:8080"
    depends_on:
      - postgres-keycloak
    volumes:
      - ../../src/keycloak/realm.json:/opt/keycloak/data/import/realm.json

  postgres-keycloak:
    image: postgres:15
    environment:
      POSTGRES_DB: keycloakdb
      POSTGRES_USER: keycloak
      POSTGRES_PASSWORD: keycloakpassword
      PGPORT: 5433
    volumes:
      - postgres_keycloak_data:/var/lib/postgresql/data

volumes:
  postgres_keycloak_data:
