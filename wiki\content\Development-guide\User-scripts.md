# User Scripts

This document describes the user scripts located in the `scripts/` directory at the root of the repository. These scripts are designed to automate common development tasks.

To run these scripts, open a PowerShell terminal, navigate to the root of the project (or to the scripts folder), and execute the desired script.

## Environment Management

### `start-dev.ps1`

Starts the development environment using Docker Compose. This includes backend services, databases, Keycloak, and observability tools.


**Usage:**
```powershell
.\scripts\start-dev.ps1
```

**Note:** 
> The UI web app will not be started automatically. You need to manually start the web app by navigating to `./src/web` and start it from there. The reason for that is that the effort to automatically recognize file changes within a docker container would be too high - that also brings some performance issues with it. Read the corresponding chapter in the `Local development` page to understand the manual tasks needed to run it.

### `stop-dev.ps1`

Stops and removes all containers and networks associated with the development environment.

**Usage:**
```powershell
.\scripts\stop-dev.ps1
```

### `start-prod.ps1`

Starts a production-like environment using Docker Compose. This is useful for testing the application in a configuration that closely resembles production. It starts all services, including the web application.

**Usage:**
```powershell
.\scripts\start-prod.ps1
```

### `stop-prod.ps1`

Stops and removes all containers, and networks associated with the production-like environment.

**Usage:**
```powershell
.\scripts\stop-prod.ps1
```

## Database Migrations

### `add-ef-migration-script.ps1`

An interactive script to create Entity Framework database migrations for the backend services. It will prompt you to select the target service (Core API or ADO API) and provide a name for the migration.

**Prerequisites:**
- .NET SDK and the EF Core command-line tool (`dotnet-ef`) must be installed.
- The development database containers must be running (`.\scripts\start-dev.ps1`).

**Usage:**
```powershell
.\scripts\add-ef-migration-script.ps1
```
