<Project>
	<PropertyGroup>
		<ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
	</PropertyGroup>

	<ItemGroup>
		<!--Core-->
		<PackageVersion Include="MediatR" Version="12.4.1" />
		<!--<PackageVersion Include="Microsoft.AspNetCore.OpenApi" Version="9.0.1" />-->
		<PackageVersion Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageVersion Include="Refit" Version="8.0.0" />
		<PackageVersion Include="Refit.HttpClientFactory" Version="8.0.0" />
		<PackageVersion Include="Swashbuckle.AspNetCore" Version="7.2.0" />
		<!-- Validation -->
		<PackageVersion Include="FluentValidation.DependencyInjectionExtensions" Version="11.11.0" />
		<!--Database-->
		<!--<PackageVersion Include="EFCore.NamingConventions" Version="9.0.0" />-->
		<PackageVersion Include="Microsoft.EntityFrameworkCore" Version="9.0.1" />
		<PackageVersion Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.1" />
		<PackageVersion Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.1" />
		<PackageVersion Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.3" />
		<!-- ALM clients -->
		<PackageVersion Include="Microsoft.TeamFoundationServer.Client" Version="19.225.1" />
		<!--Observability-->
		<PackageVersion Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.1" />
		<PackageVersion Include="OpenTelemetry.Exporter.OpenTelemetryProtocol" Version="1.12.0" />
		<!--<PackageVersion Include="OpenTelemetry.Exporter.Prometheus.AspNetCore" Version="1.12.0" />-->
		<PackageVersion Include="OpenTelemetry.Extensions.Hosting" Version="1.12.0" />
		<PackageVersion Include="OpenTelemetry.Instrumentation.AspNetCore" Version="1.12.0" />
		<PackageVersion Include="OpenTelemetry.Instrumentation.Http" Version="1.12.0" />
		<PackageVersion Include="OpenTelemetry.Instrumentation.Runtime" Version="1.12.0" />
		<PackageVersion Include="Serilog.AspNetCore" Version="9.0.0" />
		<PackageVersion Include="Serilog.Sinks.Grafana.Loki" Version="8.3.1" />
		<PackageVersion Include="Serilog" Version="4.2.0" />
	</ItemGroup>
</Project>
