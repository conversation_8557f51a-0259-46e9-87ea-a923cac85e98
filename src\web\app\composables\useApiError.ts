/**
 * Composable for handling API errors in a consistent way across the application
 *
 * Features:
 * - Processes different types of API error responses
 * - Extracts meaningful error messages for users
 * - <PERSON>les validation errors with field-specific feedback
 * - Provides consistent error display format
 * - Supports different error severity levels
 */
export function useApiError() {
	const toast = useToast();

	/**
	 * Process an API error and return a structured error object
	 */
	function processError(
		error: ApiErrorResponse,
		context?: Partial<ErrorContext>
	): ProcessedError {
		// Handle validation errors (400 with field errors)
		if (isValidationError(error)) {
			// Extract the first validation error message for the main message
			let mainMessage = 'Please check the form fields and try again.';
			if (error.errors && Object.keys(error.errors).length > 0) {
				const firstFieldErrors = Object.values(error.errors)[0];
				if (Array.isArray(firstFieldErrors) && firstFieldErrors.length > 0 && firstFieldErrors[0]) {
					mainMessage = firstFieldErrors[0];
				}
			}

			return {
				title: 'Validation Error',
				message: mainMessage,
				severity: 'error' as ErrorSeverity,
				statusCode: 400,
				fieldErrors: error.errors,
				originalError: error
			};
		}

		// Handle standard problem details
		if (isProblemDetails(error)) {
			// Use the specific error detail if available, otherwise use the title as the message
			const message = error.detail || error.title || getDefaultErrorMessage(error.status);

			return {
				title: error.title,
				message: message,
				severity: getErrorSeverity(error.status),
				statusCode: error.status,
				originalError: error
			};
		}

		// Handle API communication errors
		if (isApiCommunicationError(error)) {
			return {
				title: error.title,
				message: typeof error.detail === 'string'
					? error.detail
					: 'An error occurred while communicating with the server.',
				severity: getErrorSeverity(error.statusCode),
				statusCode: error.statusCode,
				originalError: error
			};
		}

		// Handle Nuxt/ofetch errors
		if (isFetchError(error)) {
			const statusCode = error.statusCode || error.status || 500;
			const errorData = error.data || error.response?._data;

			// Try to extract error information from the response data
			if (errorData) {
				// Check for nested data structure (Nuxt error format)
				const actualErrorData = errorData.data || errorData;

				if (isValidationError(actualErrorData)) {
					return processError(actualErrorData, context);
				}
				if (isProblemDetails(actualErrorData)) {
					return processError(actualErrorData, context);
				}
				if (isApiCommunicationError(actualErrorData)) {
					return processError(actualErrorData, context);
				}

				// Check for nested error structures that might not match our type guards
				if (actualErrorData.title || actualErrorData.detail || actualErrorData.message) {
					return {
						title: actualErrorData.title || 'Request Error',
						message: actualErrorData.detail || actualErrorData.message || getDefaultErrorMessage(statusCode),
						severity: getErrorSeverity(statusCode),
						statusCode,
						fieldErrors: actualErrorData.errors || undefined,
						originalError: error
					};
				}

				// Fallback: check the outer errorData structure
				if (errorData.title || errorData.detail || errorData.message) {
					return {
						title: errorData.title || 'Request Error',
						message: errorData.detail || errorData.message || getDefaultErrorMessage(statusCode),
						severity: getErrorSeverity(statusCode),
						statusCode,
						fieldErrors: errorData.errors || undefined,
						originalError: error
					};
				}
			}

			// Determine appropriate title based on status code
			const errorTitle = statusCode >= 400 && statusCode < 500
				? 'Request Error'
				: statusCode >= 500
					? 'Server Error'
					: 'Network Error';

			// Try to extract meaningful error message
			let errorMessage = getDefaultErrorMessage(statusCode);

			// Check if errorData has a meaningful message
			if (errorData && typeof errorData === 'object') {
				if (errorData.title && errorData.detail) {
					// Backend returned ProblemDetails-like structure
					return {
						title: errorData.title,
						message: errorData.detail,
						severity: getErrorSeverity(statusCode),
						statusCode,
						originalError: error
					};
				} else if (errorData.message) {
					errorMessage = errorData.message;
				} else if (typeof errorData === 'string') {
					errorMessage = errorData;
				}
			}

			return {
				title: errorTitle,
				message: errorMessage,
				severity: getErrorSeverity(statusCode),
				statusCode,
				originalError: error
			};
		}

		// Handle generic JavaScript errors
		if (error instanceof Error) {
			return {
				title: 'Application Error',
				message: error.message || 'An unexpected error occurred.',
				severity: 'error' as ErrorSeverity,
				originalError: error
			};
		}

		// Fallback for unknown error types
		return {
			title: 'Unknown Error',
			message: 'An unexpected error occurred. Please try again.',
			severity: 'error' as ErrorSeverity,
			originalError: error
		};
	}

	/**
	 * Display an error using toast notification
	 */
	function showError(
		error: ApiErrorResponse,
		context?: Partial<ErrorContext>
	): ProcessedError {
		const processedError = processError(error, context);

		toast.add({
			title: processedError.title,
			description: processedError.message,
			color: processedError.severity === 'warning' ? 'warning' : 'error'
		});

		return processedError;
	}

	/**
	 * Handle API errors with automatic toast display and optional custom handling
	 */
	function handleError(
		error: ApiErrorResponse,
		options?: {
			context?: Partial<ErrorContext>;
			showToast?: boolean;
			customHandler?: (processedError: ProcessedError) => void;
		}
	): ProcessedError {
		const processedError = processError(error, options?.context);

		// Show toast notification unless explicitly disabled
		if (options?.showToast !== false) {
			toast.add({
				title: processedError.title,
				description: processedError.message,
				color: processedError.severity === 'warning' ? 'warning' : 'error'
			});
		}

		// Call custom handler if provided
		if (options?.customHandler) {
			options.customHandler(processedError);
		}

		return processedError;
	}

	/**
	 * Extract field-specific error messages for form validation
	 */
	function getFieldErrors(error: ApiErrorResponse): Record<string, string[]> {
		const processedError = processError(error);
		return processedError.fieldErrors || {};
	}

	/**
	 * Check if an error has field-specific validation errors
	 */
	function hasFieldErrors(error: ApiErrorResponse): boolean {
		const fieldErrors = getFieldErrors(error);
		return Object.keys(fieldErrors).length > 0;
	}

	return {
		processError,
		showError,
		handleError,
		getFieldErrors,
		hasFieldErrors
	};
}

/**
 * Get default error message based on HTTP status code
 */
function getDefaultErrorMessage(statusCode: number): string {
	switch (statusCode) {
		case 400:
			return 'The request was invalid. Please check your input and try again.';
		case 401:
			return 'You are not authorized to perform this action. Please log in and try again.';
		case 403:
			return 'You do not have permission to perform this action.';
		case 404:
			return 'The requested resource was not found.';
		case 409:
			return 'There was a conflict with the current state. Please refresh and try again.';
		case 422:
			return 'The request could not be processed due to validation errors.';
		case 429:
			return 'Too many requests. Please wait a moment and try again.';
		case 500:
			return 'An internal server error occurred. Please try again later.';
		case 502:
			return 'The server is temporarily unavailable. Please try again later.';
		case 503:
			return 'The service is temporarily unavailable. Please try again later.';
		default:
			return 'An unexpected error occurred. Please try again.';
	}
}

/**
 * Determine error severity based on HTTP status code
 */
function getErrorSeverity(statusCode: number): ErrorSeverity {
	if (statusCode >= 400 && statusCode < 500) {
		return statusCode === 401 || statusCode === 403 ? 'warning' : 'error';
	}
	if (statusCode >= 500) {
		return 'error';
	}
	return 'info';
}
