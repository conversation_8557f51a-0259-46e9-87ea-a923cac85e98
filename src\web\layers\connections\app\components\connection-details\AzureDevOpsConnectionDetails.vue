<script setup lang="ts">
	// Props
	const props = defineProps<{
		connectionType: ConnectionType;
		providerDetails: AzureDevOpsProviderDetails;
	}>();

	// Get variant display name
	const variantDisplayName = computed(() => {
		if (props.providerDetails.variant === undefined) return "Unknown";

		const variantNames = {
			0: "Services",
			1: "Server 2019",
			2: "Server 2020",
			3: "Server 2022"
		};

		return variantNames[props.providerDetails.variant as keyof typeof variantNames] || "Unknown";
	});

	// Extract organization/server name from URL
	const organizationName = computed(() => {
		if (!props.providerDetails.adoInstanceUrl) return "Unknown";

		try {
			const url = new URL(props.providerDetails.adoInstanceUrl);

			// For Azure DevOps Services (dev.azure.com)
			if (url.hostname === "dev.azure.com") {
				const pathParts = url.pathname.split("/").filter((part) => part);
				return pathParts[0] || "Unknown";
			}

			// For Azure DevOps Server
			return url.hostname;
		} catch {
			return "Unknown";
		}
	});

	// Determine if this is Azure DevOps Services or Server
	const isServices = computed(() => {
		if (!props.providerDetails.adoInstanceUrl) return false;
		return props.providerDetails.adoInstanceUrl.includes("dev.azure.com");
	});
</script>

<template>
	<div class="space-y-3">
		<!-- Organization/Server URL -->
		<div class="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-700">
			<span class="font-medium text-gray-600 dark:text-gray-400">
				{{ isServices ? "Organization URL:" : "Server URL:" }}
			</span>
			<div class="text-right">
				<a
					v-if="providerDetails.adoInstanceUrl"
					:href="providerDetails.adoInstanceUrl"
					target="_blank"
					rel="noopener noreferrer"
					class="text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 text-sm font-mono break-all"
				>
					{{ providerDetails.adoInstanceUrl }}
					<UIcon name="i-heroicons-arrow-top-right-on-square" class="w-3 h-3 ml-1 inline" />
				</a>
				<span v-else class="text-sm text-gray-500 dark:text-gray-400">Not available</span>
			</div>
		</div>

		<!-- Organization/Server Name -->
		<div class="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-700">
			<span class="font-medium text-gray-600 dark:text-gray-400">
				{{ isServices ? "Organization:" : "Server:" }}
			</span>
			<span class="text-sm text-gray-900 dark:text-gray-100 font-medium">
				{{ organizationName }}
			</span>
		</div>

		<!-- Variant -->
		<div class="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-700">
			<span class="font-medium text-gray-600 dark:text-gray-400">Type:</span>
			<UBadge :color="isServices ? 'primary' : 'secondary'" variant="soft">
				<UIcon :name="isServices ? 'i-heroicons-cloud' : 'i-heroicons-server'" class="w-3 h-3 mr-1" />
				Azure DevOps {{ variantDisplayName }}
			</UBadge>
		</div>

		<!-- Authentication Note -->
		<div class="flex justify-between items-start py-2">
			<span class="font-medium text-gray-600 dark:text-gray-400">Authentication:</span>
			<div class="text-right">
				<div class="flex items-center text-sm text-gray-900 dark:text-gray-100">
					<UIcon name="i-heroicons-key" class="w-4 h-4 mr-1 text-green-500" />
					Personal Access Token
				</div>
				<p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Token details are not displayed for security</p>
			</div>
		</div>
	</div>
</template>
