﻿<Project Sdk="Microsoft.NET.Sdk">
	<ItemGroup>
		<PackageReference Include="FluentValidation.DependencyInjectionExtensions" />
		<PackageReference Include="MediatR" />
		<PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" />
		<PackageReference Include="Refit" />
	</ItemGroup>


	<ItemGroup>
		<ProjectReference Include="..\Core.Domain\Core.Domain.csproj" />
		<ProjectReference Include="..\..\shared\Shared.Application\Shared.Application.csproj" />
	</ItemGroup>

</Project>
