﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Ado.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class RenamingConnectionProperties : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "PAT",
                schema: "public",
                table: "Connections",
                newName: "Pat");

            migrationBuilder.RenameColumn(
                name: "Organization",
                schema: "public",
                table: "Connections",
                newName: "OrganizationName");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "<PERSON>",
                schema: "public",
                table: "Connections",
                newName: "PAT");

            migrationBuilder.RenameColumn(
                name: "OrganizationName",
                schema: "public",
                table: "Connections",
                newName: "Organization");
        }
    }
}
