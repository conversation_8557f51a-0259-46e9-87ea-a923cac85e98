using Ado.Application.Common.Interfaces;
using Ado.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using System.Reflection;

namespace Ado.Infrastructure;

public class ApplicationDbContext : DbContext, IApplicationDbContext
{
	public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
	{

	}

#if DEBUG
	public ApplicationDbContext() : base(new DbContextOptionsBuilder<ApplicationDbContext>()
		.UseNpgsql("Host=localhost:5434;Database=adodb;Username=adouser;Password=adopassword")
		.Options)
	{
		// This constructor is for design-time (migrations) only
		Console.WriteLine("WARNING: this constructor shouldn't be called during app development - only when creating an EF migration file");
	}
#endif

	public DbSet<Connection> Connections { get; set; }

	protected override void OnModelCreating(ModelBuilder builder)
	{
		builder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());

		builder.HasDefaultSchema(Schemas.Default);
	}
}
