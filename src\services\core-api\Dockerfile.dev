FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /app
EXPOSE 8081

# Install the remote debugger
RUN curl -sSL https://aka.ms/getvsdbgsh | bash /dev/stdin -v latest -l /usr/share/dotnet/vsdbg \
	&& chmod +x /usr/share/dotnet/vsdbg/vsdbg

# Copy the props files from one level above
COPY Directory.Build.props Directory.Packages.props ./

# Copy the project files and restore dependencies
COPY core-api/Core.Api/Core.Api.csproj ./core-api/Core.Api/Core.Api.csproj

WORKDIR /app/core-api/Core.Api
RUN dotnet restore

# Copy the remaining project files

WORKDIR /app
COPY core-api ./core-api

# Dev env variables
ENV ASPNETCORE_ENVIRONMENT=Development
ENV DOTNET_USE_POLLING_FILE_WATCHER=1
ENV DOTNET_WATCH_RESTART_ON_RUDE_EDIT=1

# Start the application in watch mode with debugging enabled
CMD ["dotnet", "watch", "run", "--project", "core-api/Core.Api/Core.Api.csproj", "--urls", "http://*:8081"]
