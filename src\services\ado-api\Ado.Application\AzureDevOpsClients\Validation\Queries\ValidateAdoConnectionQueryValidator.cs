using FluentValidation;

namespace Ado.Application.AzureDevOpsClients.Validation.Queries;

public class ValidateAdoConnectionQueryValidator : AbstractValidator<ValidateAdoConnectionQuery>
{
	public ValidateAdoConnectionQueryValidator()
	{
		RuleFor(c => c.Pat).NotEmpty().WithMessage("Personal Access Token (PAT) is required.");
		RuleFor(c => c.AdoInstanceUrl).Must(uri => Uri.TryCreate(uri, UriKind.Absolute, out _)).WithMessage("ADO Instance URL is required in a correct format (i.e. 'https://dev.azure.com/organization'.");
	}
}
