<script setup lang="ts">
	const { user, loggedIn } = useUserSession();
	const colorMode = useColorMode();
	const { currentFormat } = useDateFormatting();

	defineProps<{
		align?: "start" | "center" | "end";
		side?: "top" | "right" | "bottom" | "left";
	}>();

	const isDark = computed({
		get() {
			return colorMode.value === "dark";
		},
		set() {
			colorMode.preference = colorMode.value === "dark" ? "light" : "dark";
		}
	});

	const dateFormats = ref([
		{ label: "31.12.2024", value: "dd.mm.yyyy" },
		{ label: "12/31/2024", value: "mm/dd/yyyy" },
		{ label: "2024.12.31", value: "yyyy.mm.dd" }
	]);

	async function logout() {
		try {
			const response = await $fetch<{ logoutUrl: string }>("/auth/logout", { method: "POST" });
			await navigateTo(response.logoutUrl, { external: true });
		} catch (error) {
			console.log(error);
		}
	}
</script>

<template>
	<UPopover class="flex items-center justify-center" :content="{ sideOffset: 10, align: align, side: side }">
		<UButton icon="i-heroicons-cog-8-tooth" size="lg" />

		<template #content>
			<div class="flex flex-col items-center p-6">
				<p class="mb-8">Hey, {{ user?.name }}!</p>

				<p class="text-sm font-medium mb-2">Color mode</p>
				<USwitch class="mb-6" v-model="isDark" checked-icon="i-heroicons-moon" unchecked-icon="i-heroicons-sun" size="lg" />

				<p class="text-sm font-medium mb-2">Date format</p>
				<UTabs v-model="currentFormat" class="mx-2 mb-6 w-full" variant="pill" orientation="horizontal" :items="dateFormats" />

				<UButton v-if="loggedIn" @click="logout" size="lg">Logout</UButton>
			</div>
		</template>
	</UPopover>
</template>
