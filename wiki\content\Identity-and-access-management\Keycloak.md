[[_TOC_]]

# Keycloak Technical Documentation

## What is Keycloak?
Keycloak is an open-source Identity and Access Management (IAM) solution for modern applications and services. It provides features such as Single Sign-On (SSO), user federation, strong authentication, user management, and fine-grained authorization. Its simplicity comes from being running it in a simple Docker container.

## Why is Keycloak Used in our Solution?
Keycloak is used in our solution to centralize authentication and authorization for the web application and its services. It enables secure user login, role-based access control, and integration with external identity providers. This ensures a consistent and secure authentication experience across the system.

## Key Concepts

### Realms
A **realm** in Keycloak is an isolated management space for a set of client applications, users, credentials, roles, and groups. Each realm manages its own users and clients. In this solution, a single dedicated realm called `tmerge` is used for the whole application.

### Clients
A **client** in Keycloak represents an application or service that requires authentication. Clients can be web applications, REST APIs, or any service that needs to authenticate users. Each client is configured with its own settings, such as redirect URIs, client secrets, and access types (public/confidential).

## Manual Tasks in the Keycloak UI
- **User Management**: Create, update, or delete users. Assign roles and groups as needed.
- **Role Management**: Define roles for fine-grained access control. Assign roles to users or groups.
- **Client Management**: Update client settings and manage client secrets as needed.
- **Identity Provider Integration**: Configure external identity providers (e.g., Facebook, Google, Azure Entra ID, etc.) if required.
- **Export/Import Realm**: Use the realm export/import feature to back up or migrate configuration (see `src/keycloak/realm.json`).

## How Keycloak is Used in our Solution
- **Development Mode**: Keycloak runs as a container, accessible to local services for authentication and authorization.
- **Production Mode**: Keycloak is accessed through **Traefik**, which acts as a reverse proxy. Traefik routes authentication requests securely to the Keycloak service, ensuring secure communication and integration with other services.

The realm and client configuration are stored in `src/keycloak/realm.json` for reproducibility and version control.

## Integration with Applications
- **Web Application**: The Nuxt frontend app is configured as a Keycloak client. It uses OpenID Connect (OIDC) to authenticate users via Keycloak.
- **API Services**: Backend APIs validate JWT tokens issued by Keycloak to authorize requests.
- **Traefik**: In production, Traefik routes requests to Keycloak and other services, enforcing secure access.

## Additional Important Aspects
- **Environment Variables**: Keycloak configuration (admin user, password, DB connection, etc.) is typically managed via environment variables in Docker Compose files.
- **Security Best Practices**:
  - Use HTTPS in production.
  - Rotate client secrets regularly.
  - Limit admin access to the Keycloak console.
  - Enable two-factor authentication (2FA) if required.
- **Monitoring and Logging**: Monitor Keycloak logs for authentication events and errors. Integrate with centralized logging if possible.

## Database Configuration
Keycloak uses a dedicated PostgreSQL database for storing all identity, user, and configuration data. This ensures data durability and scalability for production environments.

### Provider
- **Database:** PostgreSQL 15 (container: `postgres-keycloak`)
- **Port:** 5433 (internal to Docker network)
- **Database Name:** `keycloakdb`
- **Username:** `keycloak`
- **Password:** `keycloakpassword`
- **Data Persistence:** Docker volume (`postgres_keycloak_data_dev` or `postgres_keycloak_data_prod`) depending on the compose environment

### Connection (as configured in Docker Compose)
Keycloak is configured to connect to the database using the following environment variables:
- `KC_DB`: `postgres`
- `KC_DB_URL_HOST`: `postgres-keycloak`
- `KC_DB_URL_PORT`: `5433`
- `KC_DB_URL_DATABASE`: `keycloakdb`
- `KC_DB_USERNAME`: `keycloak`
- `KC_DB_PASSWORD`: `keycloakpassword`

> **Note:** The database container is dedicated to Keycloak and should not be shared with other services. Data is persisted using Docker volumes to ensure it survives container restarts and upgrades.

### Admin Credentials
- **Keycloak Admin User:** `admin`
- **Keycloak Admin Password:** `admin`

> **Security Warning:** Default credentials and passwords are for development only. Change all secrets and credentials before deploying to production. Currently we use environment variables which is not safe - we'll have to look into Kubernetes secret management once we get there.

## Default Keycloak Configuration (Imported)
The following default configuration is imported from `src/keycloak/realm.json` and used in the Docker Compose setup:

### Realm
- **Name:** `tmerge`
- **Enabled:** true

### Clients
- **Client ID:** `nuxt-app`
- **Name:** Nuxt Application
- **Description:** Client for Nuxt App authentication
- **Enabled:** true
- **Public Client:** false (confidential)
- **Redirect URIs:**
  - `http://localhost:3000/*`
  - `http://tmerge.localhost/*`
- **Web Origins:**
  - `http://localhost:3000`
  - `http://tmerge.localhost/*`
- **Direct Access Grants Enabled:** true
- **Standard Flow Enabled:** true
- **Protocol:** openid-connect
- **Client Authenticator Type:** client-secret
- **Secret:** (see `realm.json` for actual value)
- **Service Accounts Enabled:** true
- **Authorization Services Enabled:** false
- **Implicit Flow Enabled:** false
- **Protocol Mappers:** includes mapping for client roles

### Default app users
- Test User
  - E-mail: `<EMAIL>`
  - Password: `Test@1234`
  - Roles: [`user`] 
- Admin User
  - E-mail: `<EMAIL>`
  - Password: `Test@1234`
  - Roles: [`user`, `admin`]

### Roles
- Client Roles for the `nuxt-app` client app:
  - `user`: User role for the UI app users
  - `admin`: Admin role for the UI app users

> **Note:** These default users and roles are intended for development and testing. Change passwords and review roles before deploying to production.

## References
- [Keycloak Documentation](https://www.keycloak.org/documentation)
- [OpenID Connect](https://openid.net/connect/)
- [Traefik Documentation](https://doc.traefik.io/traefik/)
