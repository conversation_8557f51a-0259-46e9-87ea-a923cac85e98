using Ado.Application.Common.Interfaces;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Shared.Application.Contracts.ConnectionDetails;
using Shared.Application.Exceptions;

namespace Ado.Application.Connections.Queries.GetConnectionDetails;

public record GetAdoConnectionDetailsQuery(Guid CoreConnectionId) : IRequest<AzureDevOpsConnectionDetails>;

public class GetAdoConnectionDetailsQueryHandler(
	IApplicationDbContext context,
	ILogger<GetAdoConnectionDetailsQueryHandler> logger) : IRequestHandler<GetAdoConnectionDetailsQuery, AzureDevOpsConnectionDetails>
{
	private readonly IApplicationDbContext _context = context;
	private readonly ILogger<GetAdoConnectionDetailsQueryHandler> _logger = logger;

	public async Task<AzureDevOpsConnectionDetails> Handle(GetAdoConnectionDetailsQuery request, CancellationToken cancellationToken)
	{
		var connection = await _context.Connections
			.Where(x => x.CoreConnectionId == request.CoreConnectionId && x.IsDeleted == false)
			.FirstOrDefaultAsync(cancellationToken);

		if (connection == null)
		{
			throw new NotFoundException($"ADO connection with core connection ID '{request.CoreConnectionId}' was not found.");
		}

		_logger.LogInformation("Retrieved ADO connection details for core connection ID: {CoreConnectionId}", request.CoreConnectionId);

		// Return only non-sensitive connection details
		return new AzureDevOpsConnectionDetails
		{
			AdoInstanceUrl = connection.AdoInstanceUrl,
			Variant = (int)connection.Variant,
			// Explicitly exclude PAT for security
		};
	}
}
