using FluentValidation;

namespace Core.Application.Connections.Commands.CreateConnection;

public class CreateConnectionCommandValidator : AbstractValidator<CreateConnectionCommand>
{
	public CreateConnectionCommandValidator()
	{
		RuleFor(c => c.Name).NotEmpty().MinimumLength(3);
		RuleFor(c => c.ConnectionTypeId).NotEmpty();
		RuleFor(c => c.Description).MinimumLength(5).When(c => !string.IsNullOrEmpty(c.Description));
		RuleFor(c => c.Payload).NotNull().WithMessage("ALM-specific payload is required for creating a connection.");
	}
}
