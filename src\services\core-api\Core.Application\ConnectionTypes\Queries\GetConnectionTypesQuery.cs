using Core.Application.Common.Interfaces;
using Core.Domain.ConnectionTypes;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Core.Application.ConnectionTypes.Queries;

public record GetConnectionTypesQuery : IRequest<List<ConnectionType>>;
public class GetConnectionTypesQueryHandler(IApplicationDbContext context) : IRequestHandler<GetConnectionTypesQuery, List<ConnectionType>>
{
	private readonly IApplicationDbContext _context = context;

	public async Task<List<ConnectionType>> Handle(GetConnectionTypesQuery request, CancellationToken cancellationToken)
	{
		var connectionTypes = await _context.ConnectionTypes
			.Where(x => x.IsDeleted == false && x.IsEnabled)
			.ToListAsync(cancellationToken);

		return connectionTypes;
	}
}

