# Production Docker Compose Files

This document describes the Docker Compose files used for the production environment, located in `docker-compose/prod/`. These files define the services needed to run the tmerge application in a production-like configuration.

## services.yml

This file defines the core application services and databases with production optimizations.

### Application Services

#### core-api
- **Purpose**: Main backend API service handling core business logic
- **Image**: Built from `src/services/core-api/Dockerfile` (production build)
- **Dependencies**: postgres-core (with health checks), ado-api
- **Database**: Connected to postgres-core
- **Optimization**: Uses production Dockerfile without development volumes

#### ado-api
- **Purpose**: Azure DevOps integration API service
- **Image**: Built from `src/services/ado-api/Dockerfile` (production build)
- **Dependencies**: postgres-ado (with health checks)
- **Database**: Connected to postgres-ado
- **Optimization**: Uses production Dockerfile without development volumes

### Database Services

#### postgres-core
- **Purpose**: Primary PostgreSQL database for core application data
- **Image**: postgres:15
- **Database**: coredb
- **Credentials**: coreuser/corepassword
- **Port**: Internal 5432
- **Volume**: postgres_core_data for data persistence
- **Health Check**: pg_isready command with 5s intervals and 5 retries

#### postgres-ado
- **Purpose**: Dedicated PostgreSQL database for ADO-related data
- **Image**: postgres:15
- **Database**: adodb
- **Credentials**: adouser/adopassword
- **Port**: Internal 5432
- **Volume**: postgres_ado_data for data persistence
- **Health Check**: pg_isready command with 5s intervals and 5 retries

## web.yml

This file defines the web application and reverse proxy services.

### Web Services

#### traefik
- **Purpose**: Reverse proxy and load balancer for routing traffic
- **Image**: traefik:v3.0
- **Port**: 80 (HTTP traffic entry point)
- **Configuration**: Custom traefik.yml configuration file
- **Dependencies**: web, grafana
- **Volumes**: Docker socket access and traefik configuration
- **Role**: Routes traffic to appropriate services based on domain

#### web
- **Purpose**: Nuxt.js frontend application
- **Image**: Built from `src/web` (production build)
- **Dependencies**: core-api, keycloak
- **Environment Variables**:
  - Session and OAuth configuration for Keycloak integration
  - API endpoints for core-api and ado-api
  - Base URL configuration for tmerge.localhost
  - Logging and debug settings
- **Networking**: Extra host mapping for keycloak.localhost

## observability.yml

This file sets up the observability stack identical to development but without exposed ports.

### Monitoring & Visualization

#### grafana
- **Purpose**: Visualization dashboard for metrics, logs, and traces
- **Image**: grafana/grafana-oss:11.6.0
- **Dependencies**: prometheus, loki, tempo, otelcol
- **Volumes**: Persistent data storage and provisioning configuration
- **Access**: Through Traefik at grafana.localhost

#### prometheus
- **Purpose**: Metrics collection and storage system
- **Image**: prom/prometheus:v2.55.0
- **Configuration**: Custom prometheus.yml with 200h retention
- **Features**: Web UI, lifecycle management, remote write receiver
- **Volume**: prometheus_data for metrics storage

### Logging & Tracing

#### loki
- **Purpose**: Log aggregation system for centralized logging
- **Image**: grafana/loki:3.4.4
- **Volume**: loki_data for log storage
- **Integration**: Works with Grafana for log visualization

#### tempo
- **Purpose**: Distributed tracing backend for request tracing
- **Image**: grafana/tempo:2.6.1
- **Configuration**: Custom tempo.yaml configuration
- **Volume**: tempo_data for trace storage
- **Integration**: Works with Grafana for trace visualization

#### otelcol
- **Purpose**: OpenTelemetry Collector for telemetry data processing
- **Image**: otel/opentelemetry-collector-contrib:0.117.0
- **Configuration**: Custom otelcol.yaml configuration
- **Role**: Collects, processes, and exports telemetry data to backends

## keycloak.yml

This file sets up the authentication and authorization services.

### Authentication Services

#### keycloak
- **Purpose**: Identity and access management system
- **Image**: quay.io/keycloak/keycloak:24.0
- **Mode**: Development mode with auto-import (same as dev)
- **Admin Credentials**: admin/admin
- **Database**: Connected to postgres-keycloak
- **Configuration**: Imports realm configuration from realm.json
- **Access**: Through Traefik at keycloak.localhost

#### postgres-keycloak
- **Purpose**: Dedicated PostgreSQL database for Keycloak data
- **Image**: postgres:15
- **Database**: keycloakdb
- **Credentials**: keycloak/keycloakpassword
- **Port**: Internal 5433
- **Volume**: postgres_keycloak_data for data persistence

## Key Differences from Development

1. **No Exposed Ports**: Services are accessed through Traefik reverse proxy
2. **Health Checks**: Production databases include health checks for reliable startup
3. **Production Builds**: Uses production Dockerfiles without development volumes
4. **Domain-based Access**: Services are accessed via *.localhost domains
5. **Service Dependencies**: Explicit dependency chains with health check conditions

## Usage

To start the complete production environment:

```powershell
.\scripts\start-prod.ps1
```

This will start all services defined in these four compose files with the project name `tmerge-prod`.

## Access URLs

- **Web App**: http://tmerge.localhost
- **Keycloak**: http://keycloak.localhost
- **Grafana**: http://grafana.localhost
- **Traefik Dashboard**: http://traefik.localhost
