using Ado.Application.Connections.Commands.CreateConnection;
using Ado.Application.Connections.Queries.GetConnectionDetails;
using Ado.Domain.Entities;
using Microsoft.AspNetCore.Mvc;
using Shared.Application.Contracts.ConnectionDetails;

namespace Ado.Api.Controllers
{
	[ApiController]
	[Route("api/[controller]")]
	public class ConnectionsController(ILogger<ConnectionsController> logger, ISender mediator) : ControllerBase
	{
		private readonly ILogger<ConnectionsController> _logger = logger;
		private readonly ISender _mediator = mediator;

		[HttpGet("{coreConnectionId:guid}")]
		public async Task<AzureDevOpsConnectionDetails> GetByCoreConnectionId(Guid coreConnectionId)
		{
			_logger.LogInformation("Fetching ADO connection details for core connection ID: {CoreConnectionId}", coreConnectionId);
			var connectionDetails = await _mediator.Send(new GetAdoConnectionDetailsQuery(coreConnectionId));

			return connectionDetails;
		}

		[HttpPost]
		public async Task<Connection> Post([FromBody] CreateConnectionCommand command)
		{
			_logger.LogInformation("Creating a new ADO connection.");
			var connection = await _mediator.Send(command);

			return connection;
		}
	}
}
