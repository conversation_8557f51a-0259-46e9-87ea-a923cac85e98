#See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
USER app
WORKDIR /app
EXPOSE 8082

FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src

# Copy props files
COPY Directory.Build.props Directory.Packages.props ./
# Copy the source code folders
COPY ado-api ./ado-api
COPY shared ./shared
# Restore
WORKDIR /src/ado-api/Ado.Api
RUN dotnet restore "./Ado.Api.csproj"

# Build
RUN dotnet build "./Ado.Api.csproj" -c $BUILD_CONFIGURATION -o /app/build

FROM build AS publish
ARG BUILD_CONFIGURATION=Release
WORKDIR /src/ado-api/Ado.Api
RUN dotnet publish "./Ado.Api.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Ado.Api.dll", "--urls", "http://+:8082"]
