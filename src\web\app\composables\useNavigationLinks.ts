import type { NavigationMenuItem } from "@nuxt/ui";

export async function useNavigationLinks() {
	const { isAdmin } = useUserRoles();

	const links = computed(() => {
		const links: NavigationMenuItem[] = [];

		// Dashboard
		links.push({
			label: "Dashboard", icon: "i-heroicons-home", to: "/"
		});


		// Connections
		links.push({
			label: "Connections", icon: "i-heroicons-link", disabled: true, open: true, collapsible: false, children: [
				{ label: "Manage connections", icon: "i-heroicons-adjustments-horizontal", to: "/connections" },
				{ label: "Connection types", icon: "i-heroicons-signal", to: "/connectionTypes" },
			]
		});

		// Settings for admins
		if (isAdmin.value) {
			links.push({
				label: "Settings", icon: "i-heroicons-cog-6-tooth", disabled: true, open: true, collapsible: false, children: [
					// { label: "General settings", icon: "i-heroicons-adjustments-vertical", to: "/connections" },
					// { label: "Audit logs", icon: "i-heroicons-document-text", to: "/connections" },
				]
			});
		}

		return links;
	});

	return {
		links
	};
}

