using Core.Application.External;
using Core.Application.External.Interfaces;
using Core.Application.External.Services;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Shared.Application.Behaviors;
using System.Reflection;

namespace Core.Application;

public static class DependencyInjection
{
	public static IServiceCollection AddApplication(this IServiceCollection services)
	{
		services.AddMediatR(config =>
		{
			config.RegisterServicesFromAssembly(typeof(DependencyInjection).Assembly);

			config.AddBehavior(typeof(IPipelineBehavior<,>), typeof(ValidationBehaviour<,>));
		});

		services.AddValidatorsFromAssembly(Assembly.GetExecutingAssembly());

		services.AddScoped<IExternalServiceResolver, ExternalServiceResolver>();

		services.AddScoped<IProviderHandler, AdoProviderHandler>();

		return services;
	}
}
