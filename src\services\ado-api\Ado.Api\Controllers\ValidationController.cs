using Ado.Application.AzureDevOpsClients.Validation.Queries;
using Microsoft.AspNetCore.Mvc;

namespace Ado.Api.Controllers
{
	[ApiController]
	[Route("api/[controller]")]
	public class ValidationController(ILogger<ValidationController> logger, ISender mediator) : ControllerBase
	{
		private readonly ILogger<ValidationController> _logger = logger;
		private readonly ISender _mediator = mediator;

		[HttpPost]
		public async Task<bool> Post([FromBody] ValidateAdoConnectionQuery query)
		{
			_logger.LogInformation("Validating ADO connection for URL: '{AdoInstanceUrl}'.", query.AdoInstanceUrl);
			await _mediator.Send(query);

			return true;
		}
	}
}
