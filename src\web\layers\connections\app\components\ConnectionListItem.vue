<script setup lang="ts">
	import { getConnectionTypeImage, hasConnectionTypeImage } from '../utils/connection-images';

	const props = defineProps<{
		connection: Connection;
	}>();

	// Get the image configuration for this connection type
	const connectionImage = computed(() => getConnectionTypeImage(props.connection.connectionType));
	const hasCustomImage = computed(() => hasConnectionTypeImage(props.connection.connectionType));

	// Track if image loading failed
	const imageLoadFailed = ref(false);
	const shouldShowImage = computed(() => hasCustomImage.value && !imageLoadFailed.value);

	// Handle image load error
	function handleImageError() {
		imageLoadFailed.value = true;
	}
</script>

<template>
	<UCard class="w-full">
		<div class="space-y-4">
			<!-- Header with connection name and type image/icon -->
			<div class="flex items-start gap-4">
				<div class="flex-shrink-0">
					<!-- Connection type image or fallback icon -->
					<div class="w-35 h-30 overflow-hidden bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm flex items-center justify-center p-2">
						<img
							v-if="shouldShowImage"
							:src="connectionImage.src"
							:alt="connectionImage.alt"
							class="w-full h-full object-contain filter drop-shadow-sm dark:brightness-90 dark:contrast-110"
							loading="lazy"
							@error="handleImageError"
						/>
						<UIcon
							v-else
							:name="connectionImage.fallbackIcon || 'i-heroicons-link'"
							class="w-6 h-6 text-gray-500 dark:text-gray-400"
						/>
					</div>
				</div>

				<div class="flex-1 min-w-0">
					<h3 class="text-xl font-semibold text-gray-900 dark:text-gray-100 truncate">
						{{ connection.name }}
					</h3>
					<p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
						{{ connection.connectionType.name }}
					</p>
				</div>
			</div>

			<!-- Description -->
			<div v-if="connection.description" class="text-gray-600 dark:text-gray-300 text-sm">
				{{ connection.description }}
			</div>

			<!-- Connection details -->
			<div class="flex items-center justify-between">
				<div class="text-xs text-gray-500 dark:text-gray-400">
					<span class="font-medium">Service:</span>
					{{ connection.connectionType.serviceName }}
				</div>

				<UButton
					@click="navigateTo(`connections/${connection.id}`)"
					variant="outline"
					size="sm"
					icon="i-heroicons-arrow-right"
				>
					View Details
				</UButton>
			</div>
		</div>
	</UCard>
</template>
