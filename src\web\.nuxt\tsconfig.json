// Generated by nuxi
{
  "compilerOptions": {
    "paths": {
      "nitropack/types": [
        "../node_modules/.pnpm/nitropack@2.11.12/node_modules/nitropack/types"
      ],
      "nitropack/runtime": [
        "../node_modules/.pnpm/nitropack@2.11.12/node_modules/nitropack/runtime"
      ],
      "nitropack": [
        "../node_modules/.pnpm/nitropack@2.11.12/node_modules/nitropack"
      ],
      "defu": [
        "../node_modules/.pnpm/defu@6.1.4/node_modules/defu"
      ],
      "h3": [
        "../node_modules/.pnpm/h3@1.15.3/node_modules/h3"
      ],
      "consola": [
        "../node_modules/.pnpm/consola@3.4.2/node_modules/consola"
      ],
      "ofetch": [
        "../node_modules/.pnpm/ofetch@1.4.1/node_modules/ofetch"
      ],
      "@unhead/vue": [
        "../node_modules/.pnpm/@unhead+vue@2.0.9_vue@3.5.14_typescript@5.8.3_/node_modules/@unhead/vue"
      ],
      "@nuxt/devtools": [
        "../node_modules/.pnpm/@nuxt+devtools@2.4.1_vite@6_1d85cc3f204fd1b0734164c2788bcaae/node_modules/@nuxt/devtools"
      ],
      "@vue/runtime-core": [
        "../node_modules/.pnpm/@vue+runtime-core@3.5.14/node_modules/@vue/runtime-core"
      ],
      "@vue/compiler-sfc": [
        "../node_modules/.pnpm/@vue+compiler-sfc@3.5.14/node_modules/@vue/compiler-sfc"
      ],
      "unplugin-vue-router/client": [
        "../node_modules/.pnpm/unplugin-vue-router@0.12.0__342e1c33f39dae618c97ca8e17bbf51f/node_modules/unplugin-vue-router/client"
      ],
      "@nuxt/schema": [
        "../node_modules/.pnpm/@nuxt+schema@3.17.4/node_modules/@nuxt/schema"
      ],
      "nuxt": [
        "../node_modules/.pnpm/nuxt@3.17.3_@parcel+watcher_a79f837be77ab0e2d4d7d7938f785c76/node_modules/nuxt"
      ],
      "vite/client": [
        "../node_modules/.pnpm/vite@6.3.5_@types+node@22.1_e8cc0a0d44f5bbcf108ec2f958149ae9/node_modules/vite/client"
      ],
      "~": [
        "../app"
      ],
      "~/*": [
        "../app/*"
      ],
      "@": [
        "../app"
      ],
      "@/*": [
        "../app/*"
      ],
      "~~": [
        ".."
      ],
      "~~/*": [
        "../*"
      ],
      "@@": [
        ".."
      ],
      "@@/*": [
        "../*"
      ],
      "#shared": [
        "../shared"
      ],
      "#shared/*": [
        "../shared/*"
      ],
      "assets": [
        "../app/assets"
      ],
      "assets/*": [
        "../app/assets/*"
      ],
      "public": [
        "../public"
      ],
      "public/*": [
        "../public/*"
      ],
      "#layers/connections": [
        "../layers/connections"
      ],
      "#layers/connections/*": [
        "../layers/connections/*"
      ],
      "#layers/dashboard": [
        "../layers/dashboard"
      ],
      "#layers/dashboard/*": [
        "../layers/dashboard/*"
      ],
      "#app": [
        "../node_modules/.pnpm/nuxt@3.17.3_@parcel+watcher_a79f837be77ab0e2d4d7d7938f785c76/node_modules/nuxt/dist/app"
      ],
      "#app/*": [
        "../node_modules/.pnpm/nuxt@3.17.3_@parcel+watcher_a79f837be77ab0e2d4d7d7938f785c76/node_modules/nuxt/dist/app/*"
      ],
      "vue-demi": [
        "../node_modules/.pnpm/nuxt@3.17.3_@parcel+watcher_a79f837be77ab0e2d4d7d7938f785c76/node_modules/nuxt/dist/app/compat/vue-demi"
      ],
      "#ui": [
        "../node_modules/.pnpm/@nuxt+ui@3.1.2_@babel+parse_f07273e8df9b8a2f3a26358e2de67989/node_modules/@nuxt/ui/dist/runtime"
      ],
      "#ui/*": [
        "../node_modules/.pnpm/@nuxt+ui@3.1.2_@babel+parse_f07273e8df9b8a2f3a26358e2de67989/node_modules/@nuxt/ui/dist/runtime/*"
      ],
      "#color-mode-options": [
        "./color-mode-options.mjs"
      ],
      "#auth-utils": [
        "../node_modules/.pnpm/nuxt-auth-utils@0.5.20_patc_7b0d61e75377a67e14def2045f8741f3/node_modules/nuxt-auth-utils/dist/runtime/types/index"
      ],
      "#vue-router": [
        "../node_modules/.pnpm/vue-router@4.5.1_vue@3.5.14_typescript@5.8.3_/node_modules/vue-router"
      ],
      "#unhead/composables": [
        "../node_modules/.pnpm/nuxt@3.17.3_@parcel+watcher_a79f837be77ab0e2d4d7d7938f785c76/node_modules/nuxt/dist/head/runtime/composables/v4"
      ],
      "#imports": [
        "./imports"
      ],
      "#app-manifest": [
        "./manifest/meta/dev"
      ],
      "#components": [
        "./components"
      ],
      "#build": [
        "."
      ],
      "#build/*": [
        "./*"
      ]
    },
    "esModuleInterop": true,
    "skipLibCheck": true,
    "target": "ESNext",
    "allowJs": true,
    "resolveJsonModule": true,
    "moduleDetection": "force",
    "isolatedModules": true,
    "verbatimModuleSyntax": true,
    "strict": true,
    "noUncheckedIndexedAccess": true,
    "forceConsistentCasingInFileNames": true,
    "noImplicitOverride": true,
    "module": "preserve",
    "noEmit": true,
    "lib": [
      "ESNext",
      "dom",
      "dom.iterable",
      "webworker"
    ],
    "jsx": "preserve",
    "jsxImportSource": "vue",
    "types": [],
    "moduleResolution": "Bundler",
    "useDefineForClassFields": true,
    "noImplicitThis": true,
    "allowSyntheticDefaultImports": true
  },
  "include": [
    "../**/*",
    "../.config/nuxt.*",
    "./nuxt.d.ts",
    "../app/**/*",
    "../node_modules/.pnpm/@nuxt+icon@1.13.0_magicast@_23101821a1a51355d9567424d10baa69/node_modules/@nuxt/icon/runtime",
    "../node_modules/.pnpm/@nuxt+icon@1.13.0_magicast@_23101821a1a51355d9567424d10baa69/node_modules/@nuxt/icon/dist/runtime",
    "../node_modules/.pnpm/@nuxt+fonts@0.11.4_db0@0.3._e13594e9f9fac8b2eb0e1b1a74be369a/node_modules/@nuxt/fonts/runtime",
    "../node_modules/.pnpm/@nuxt+fonts@0.11.4_db0@0.3._e13594e9f9fac8b2eb0e1b1a74be369a/node_modules/@nuxt/fonts/dist/runtime",
    "../node_modules/.pnpm/@nuxtjs+color-mode@3.5.2_magicast@0.3.5/node_modules/@nuxtjs/color-mode/runtime",
    "../node_modules/.pnpm/@nuxtjs+color-mode@3.5.2_magicast@0.3.5/node_modules/@nuxtjs/color-mode/dist/runtime",
    "../node_modules/.pnpm/@nuxt+ui@3.1.2_@babel+parse_f07273e8df9b8a2f3a26358e2de67989/node_modules/@nuxt/ui/runtime",
    "../node_modules/.pnpm/@nuxt+ui@3.1.2_@babel+parse_f07273e8df9b8a2f3a26358e2de67989/node_modules/@nuxt/ui/dist/runtime",
    "../node_modules/.pnpm/@pinia+nuxt@0.11.0_magicast_a67d88851b9ae3b0828837d8cf9ec91f/node_modules/@pinia/nuxt/runtime",
    "../node_modules/.pnpm/@pinia+nuxt@0.11.0_magicast_a67d88851b9ae3b0828837d8cf9ec91f/node_modules/@pinia/nuxt/dist/runtime",
    "../node_modules/.pnpm/@vueuse+nuxt@13.2.0_magicas_a4bc0199d4b0754ce6462171cad24000/node_modules/@vueuse/nuxt/runtime",
    "../node_modules/.pnpm/@vueuse+nuxt@13.2.0_magicas_a4bc0199d4b0754ce6462171cad24000/node_modules/@vueuse/nuxt/dist/runtime",
    "../node_modules/.pnpm/nuxt-auth-utils@0.5.20_patc_7b0d61e75377a67e14def2045f8741f3/node_modules/nuxt-auth-utils/runtime",
    "../node_modules/.pnpm/nuxt-auth-utils@0.5.20_patc_7b0d61e75377a67e14def2045f8741f3/node_modules/nuxt-auth-utils/dist/runtime",
    "../node_modules/.pnpm/@nuxt+devtools@2.4.1_vite@6_1d85cc3f204fd1b0734164c2788bcaae/node_modules/@nuxt/devtools/runtime",
    "../node_modules/.pnpm/@nuxt+devtools@2.4.1_vite@6_1d85cc3f204fd1b0734164c2788bcaae/node_modules/@nuxt/devtools/dist/runtime",
    "../node_modules/.pnpm/@nuxt+telemetry@2.6.6_magicast@0.3.5/node_modules/@nuxt/telemetry/runtime",
    "../node_modules/.pnpm/@nuxt+telemetry@2.6.6_magicast@0.3.5/node_modules/@nuxt/telemetry/dist/runtime",
    ".."
  ],
  "exclude": [
    "../dist",
    "../.data",
    "../node_modules",
    "../../../node_modules",
    "../node_modules/.pnpm/nuxt@3.17.3_@parcel+watcher_a79f837be77ab0e2d4d7d7938f785c76/node_modules/nuxt/node_modules",
    "../node_modules/.pnpm/@nuxt+icon@1.13.0_magicast@_23101821a1a51355d9567424d10baa69/node_modules/@nuxt/icon/node_modules",
    "../node_modules/.pnpm/@nuxt+fonts@0.11.4_db0@0.3._e13594e9f9fac8b2eb0e1b1a74be369a/node_modules/@nuxt/fonts/node_modules",
    "../node_modules/.pnpm/@nuxtjs+color-mode@3.5.2_magicast@0.3.5/node_modules/@nuxtjs/color-mode/node_modules",
    "../node_modules/.pnpm/@nuxt+ui@3.1.2_@babel+parse_f07273e8df9b8a2f3a26358e2de67989/node_modules/@nuxt/ui/node_modules",
    "../node_modules/.pnpm/@pinia+nuxt@0.11.0_magicast_a67d88851b9ae3b0828837d8cf9ec91f/node_modules/@pinia/nuxt/node_modules",
    "../node_modules/.pnpm/@vueuse+nuxt@13.2.0_magicas_a4bc0199d4b0754ce6462171cad24000/node_modules/@vueuse/nuxt/node_modules",
    "../node_modules/.pnpm/nuxt-auth-utils@0.5.20_patc_7b0d61e75377a67e14def2045f8741f3/node_modules/nuxt-auth-utils/node_modules",
    "../node_modules/.pnpm/@nuxt+devtools@2.4.1_vite@6_1d85cc3f204fd1b0734164c2788bcaae/node_modules/@nuxt/devtools/node_modules",
    "../node_modules/.pnpm/@nuxt+telemetry@2.6.6_magicast@0.3.5/node_modules/@nuxt/telemetry/node_modules",
    "../node_modules/.pnpm/@nuxt+icon@1.13.0_magicast@_23101821a1a51355d9567424d10baa69/node_modules/@nuxt/icon/runtime/server",
    "../node_modules/.pnpm/@nuxt+icon@1.13.0_magicast@_23101821a1a51355d9567424d10baa69/node_modules/@nuxt/icon/dist/runtime/server",
    "../node_modules/.pnpm/@nuxt+fonts@0.11.4_db0@0.3._e13594e9f9fac8b2eb0e1b1a74be369a/node_modules/@nuxt/fonts/runtime/server",
    "../node_modules/.pnpm/@nuxt+fonts@0.11.4_db0@0.3._e13594e9f9fac8b2eb0e1b1a74be369a/node_modules/@nuxt/fonts/dist/runtime/server",
    "../node_modules/.pnpm/@nuxtjs+color-mode@3.5.2_magicast@0.3.5/node_modules/@nuxtjs/color-mode/runtime/server",
    "../node_modules/.pnpm/@nuxtjs+color-mode@3.5.2_magicast@0.3.5/node_modules/@nuxtjs/color-mode/dist/runtime/server",
    "../node_modules/.pnpm/@nuxt+ui@3.1.2_@babel+parse_f07273e8df9b8a2f3a26358e2de67989/node_modules/@nuxt/ui/runtime/server",
    "../node_modules/.pnpm/@nuxt+ui@3.1.2_@babel+parse_f07273e8df9b8a2f3a26358e2de67989/node_modules/@nuxt/ui/dist/runtime/server",
    "../node_modules/.pnpm/@pinia+nuxt@0.11.0_magicast_a67d88851b9ae3b0828837d8cf9ec91f/node_modules/@pinia/nuxt/runtime/server",
    "../node_modules/.pnpm/@pinia+nuxt@0.11.0_magicast_a67d88851b9ae3b0828837d8cf9ec91f/node_modules/@pinia/nuxt/dist/runtime/server",
    "../node_modules/.pnpm/@vueuse+nuxt@13.2.0_magicas_a4bc0199d4b0754ce6462171cad24000/node_modules/@vueuse/nuxt/runtime/server",
    "../node_modules/.pnpm/@vueuse+nuxt@13.2.0_magicas_a4bc0199d4b0754ce6462171cad24000/node_modules/@vueuse/nuxt/dist/runtime/server",
    "../node_modules/.pnpm/nuxt-auth-utils@0.5.20_patc_7b0d61e75377a67e14def2045f8741f3/node_modules/nuxt-auth-utils/runtime/server",
    "../node_modules/.pnpm/nuxt-auth-utils@0.5.20_patc_7b0d61e75377a67e14def2045f8741f3/node_modules/nuxt-auth-utils/dist/runtime/server",
    "../node_modules/.pnpm/@nuxt+devtools@2.4.1_vite@6_1d85cc3f204fd1b0734164c2788bcaae/node_modules/@nuxt/devtools/runtime/server",
    "../node_modules/.pnpm/@nuxt+devtools@2.4.1_vite@6_1d85cc3f204fd1b0734164c2788bcaae/node_modules/@nuxt/devtools/dist/runtime/server",
    "../node_modules/.pnpm/@nuxt+telemetry@2.6.6_magicast@0.3.5/node_modules/@nuxt/telemetry/runtime/server",
    "../node_modules/.pnpm/@nuxt+telemetry@2.6.6_magicast@0.3.5/node_modules/@nuxt/telemetry/dist/runtime/server",
    "../.output"
  ]
}