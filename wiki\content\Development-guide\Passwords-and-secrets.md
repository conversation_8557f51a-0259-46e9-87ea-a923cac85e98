# Passwords and Secrets

This document lists all secrets, passwords, and configuration variables used by the tmerge application containers and web app during local development. These are organized by environment (development vs production) and by service.

> **⚠️ Security Warning**: All credentials listed here are for development purposes only. In production environments, these should be replaced with secure, randomly generated values and managed through proper secret management systems.

## Development Environment

### Database Credentials

#### PostgreSQL - Core Database
- **Container**: `postgres-core`
- **Port**: 5433 (external), 5432 (internal)
- **Database**: `coredb`
- **Username**: `coreuser`
- **Password**: `corepassword`

#### PostgreSQL - ADO Database
- **Container**: `postgres-ado`
- **Port**: 5434 (external), 5432 (internal)
- **Database**: `adodb`
- **Username**: `adouser`
- **Password**: `adopassword`

#### PostgreSQL - Keycloak Database
- **Container**: `postgres-keycloak`
- **Port**: 5433 (internal)
- **Database**: `keycloakdb`
- **Username**: `keycloak`
- **Password**: `keycloakpassword`

### Keycloak Configuration

#### Admin Credentials
- **Admin Username**: `admin`
- **Admin Password**: `admin`
- **Access URL**: http://localhost:8080

#### OAuth Client Configuration
- **Client ID**: `nuxt-app`
- **Client Secret**: `kc9LGZjbQ3Bf5wCXnCrpVBB6AYTXyBaK`
- **Realm**: `tmerge`
- **Server URL**: `http://localhost:8080`

### Web Application (Local Development)

When running `pnpm dev` in `src/web/`, the following environment variables are used that can be found in the `.env` file:

#### Session & Authentication
- **NUXT_SESSION_PASSWORD**: Random string (32+ characters) - *User must generate*
- **NUXT_OAUTH_KEYCLOAK_CLIENT_ID**: `nuxt-app`
- **NUXT_OAUTH_KEYCLOAK_CLIENT_SECRET**: `kc9LGZjbQ3Bf5wCXnCrpVBB6AYTXyBaK`
- **NUXT_OAUTH_KEYCLOAK_SERVER_URL**: `http://localhost:8080`
- **NUXT_OAUTH_KEYCLOAK_SERVER_URL_INTERNAL**: `http://keycloak:8080`
- **NUXT_OAUTH_KEYCLOAK_REALM**: `tmerge`

#### API Endpoints
- **CORE_API_URL**: `http://localhost:8081`
- **ADO_API_URL**: `http://localhost:8082`
- **BASE_URL**: `http://localhost:3000`

### Service Ports (Development)

| Service | External Port | Internal Port | Access URL |
|---------|---------------|---------------|------------|
| Core API | 8081 | 8081 | http://localhost:8081 |
| ADO API | 8082 | 8082 | http://localhost:8082 |
| Keycloak | 8080 | 8080 | http://localhost:8080 |
| Grafana | 5341 | 3000 | http://localhost:5341 |
| Web App | 3000 | 3000 | http://localhost:3000 |
| Core DB | 5433 | 5432 | localhost:5433 |
| ADO DB | 5434 | 5432 | localhost:5434 |

## Production Environment

### Database Credentials

#### PostgreSQL - Core Database
- **Container**: `postgres-core`
- **Port**: 5432 (internal only)
- **Database**: `coredb`
- **Username**: `coreuser`
- **Password**: `corepassword`

#### PostgreSQL - ADO Database
- **Container**: `postgres-ado`
- **Port**: 5432 (internal only)
- **Database**: `adodb`
- **Username**: `adouser`
- **Password**: `adopassword`

#### PostgreSQL - Keycloak Database
- **Container**: `postgres-keycloak`
- **Port**: 5433 (internal only)
- **Database**: `keycloakdb`
- **Username**: `keycloak`
- **Password**: `keycloakpassword`

### Keycloak Configuration

#### Admin Credentials
- **Admin Username**: `admin`
- **Admin Password**: `admin`
- **Access URL**: http://keycloak.localhost

#### OAuth Client Configuration
- **Client ID**: `nuxt-app`
- **Client Secret**: `kc9LGZjbQ3Bf5wCXnCrpVBB6AYTXyBaK`
- **Realm**: `tmerge`
- **Server URL**: `http://keycloak.localhost`
- **Internal Server URL**: `http://keycloak:8080`

### Web Application (Production)

The web application container uses the following environment variables:

#### Session & Authentication
- **NUXT_SESSION_PASSWORD**: `a3ef07459d934d21a1a63402e9e8cfb3`
- **NUXT_OAUTH_KEYCLOAK_CLIENT_ID**: `nuxt-app`
- **NUXT_OAUTH_KEYCLOAK_CLIENT_SECRET**: `kc9LGZjbQ3Bf5wCXnCrpVBB6AYTXyBaK`
- **NUXT_OAUTH_KEYCLOAK_SERVER_URL**: `http://keycloak.localhost`
- **NUXT_OAUTH_KEYCLOAK_SERVER_URL_INTERNAL**: `http://keycloak:8080`
- **NUXT_OAUTH_KEYCLOAK_REALM**: `tmerge`

#### API Endpoints
- **CORE_API_URL**: `http://core-api:8081`
- **ADO_API_URL**: `http://ado-api:8082`
- **BASE_URL**: `http://tmerge.localhost`

#### Logging
These are only for debugging purposes in productive environments for easier troubleshooting.
- **NITRO_LOG_LEVEL**: `info`
- **DEBUG**: `*`

### Service Access (Production)

All services are accessed through domain names via Traefik reverse proxy:

| Service | Access URL | Internal Port |
|---------|------------|---------------|
| Web App | http://tmerge.localhost | 3000 |
| Keycloak | http://keycloak.localhost | 8080 |
| Grafana | http://grafana.localhost | 3000 |
| Traefik Dashboard | http://traefik.localhost | 8080 |

## Environment Variables by Service

### Core API (.NET)
- **ASPNETCORE_ENVIRONMENT**: `Development` (dev) / `Production` (prod)
- **DOTNET_USE_POLLING_FILE_WATCHER**: `1` (dev only)
- **DOTNET_WATCH_RESTART_ON_RUDE_EDIT**: `1` (dev only)

### ADO API (.NET)
- **ASPNETCORE_ENVIRONMENT**: `Development` (dev) / `Production` (prod)
- **DOTNET_USE_POLLING_FILE_WATCHER**: `1` (dev only)
- **DOTNET_WATCH_RESTART_ON_RUDE_EDIT**: `1` (dev only)

### Keycloak
- **KC_DB**: `postgres`
- **KC_DB_URL_HOST**: `postgres-keycloak`
- **KC_DB_URL_PORT**: `5433`
- **KC_DB_URL_DATABASE**: `keycloakdb`
- **KC_DB_USERNAME**: `keycloak`
- **KC_DB_PASSWORD**: `keycloakpassword`
- **KC_IMPORT**: `/opt/keycloak/data/import/realm.json`
- **KEYCLOAK_ADMIN**: `admin`
- **KEYCLOAK_ADMIN_PASSWORD**: `admin`

## Security Recommendations

### For Development
1. **Never commit `.env` files** with real secrets to version control
2. **Use `.env.example`** as a template for required environment variables
3. **Generate unique session passwords** for each developer environment
4. **Rotate client secrets** periodically even in development

### For Production
1. **Replace all default passwords** with strong, randomly generated values
2. **Use proper secret management** (Kubernetes secrets, HashiCorp Vault, etc.)
3. **Enable HTTPS** for all external-facing services
4. **Implement proper certificate management**
5. **Use environment-specific client secrets** for OAuth
6. **Enable database SSL/TLS connections**
7. **Implement proper backup and recovery** for database credentials
8. **Monitor and audit** secret access and usage

### Credential Rotation Schedule
- **Database passwords**: Every 90 days
- **OAuth client secrets**: Every 60 days
- **Session passwords**: Every 30 days
- **Admin passwords**: Every 30 days

## Getting Started

### Development Setup
1. Copy `src/web/.env.example` to `src/web/.env`
2. Generate a secure session password (32+ characters)
3. Update any other environment variables as needed
4. Run `.\scripts\start-dev.ps1` to start the development environment

### Production Setup
1. Replace all default credentials with secure values
2. Update Docker Compose files with new credentials
3. Ensure proper secret management is in place
4. Run `.\scripts\start-prod.ps1` to start the production environment
