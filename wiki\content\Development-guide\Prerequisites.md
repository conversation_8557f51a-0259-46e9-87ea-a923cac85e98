# Prerequisites

This document lists all the dependencies required to compile, develop and run the tmerge application locally.


## Container & Infrastructure Dependencies

### Docker & Orchestration
- **Docker Desktop** (or Docker Engine) - For containerized development environment
- **Docker Compose** - For orchestrating multi-container setup

> If you only plan to run the application locally in production mode, you don't need to install anything else. Docker (Desktop) is the only application needed to spin up the application.

> If you want to develop and change the application in development mode (while getting a live feedback with your changes), you need to install the dependencies listed below.



## Core Runtime Dependencies

### .NET Development
- **.NET 9.0 SDK**
  - Required for installing the `dotnet ef` tool
  - We don't directly start the .NET applications locally - they run in a container in watch mode - so the SDK is not needed for the .NET development at all
  - Get it from https://dotnet.microsoft.com/en-us/download/dotnet/9.0
  - The .NET SDK comes pre-installed with the latest Visual Studio distributions. If you only use VS Code for development purposes, only then you need to install it manually
- **Entity Framework Core Tools** - For database migrations and management
  - Can be installed globally: `dotnet tool install --global dotnet-ef`
  - It's not part of the .NET SDK, you'll only need this tool if you want to make some changes to the services' database schemas

### Node.js Development
- **Node.js 20.11+** - Required for the web frontend (for local development)
  - including `npm`
- **pnpm 10.8.0+** - Package manager for Node.js dependencies
  - Install globally: `npm install -g pnpm@10.6.2`
- **nuxi** (latest) - Nuxt CLI tool
  - Install globally: `npm install -g nuxi@latest`



## Optional Dependencies

### Development Debugging
- **Visual Studio Debugger** - Remote debugging support is configured in Docker containers
- **VS Code** with appropriate extensions for .NET and Vue.js development
  - VS Code extensions listed in `~/.vscode/extensions.json`

### Database Management
- **pgAdmin** or similar PostgreSQL management tool (optional, for database administration)
