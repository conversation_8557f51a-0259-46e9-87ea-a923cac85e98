export default defineNuxtRouteMiddleware((to) => {
	const { loggedIn } = useUserSession();
	// Don't redirect if already on auth page to prevent loops
	if (to.path.startsWith("/auth/")) {
		return;
	}
	if (!loggedIn.value) {
		try {
			// On server side, return 401 instead of redirect
			if (import.meta.server) {
				return navigateTo("/auth/keycloak", { redirectCode: 401 });
			}
			// On client side, perform regular navigation
			return navigateTo("/auth/keycloak");
		} catch (error) {
			console.error("Navigation error:", error);
			return abortNavigation(error as Error);
		}
	}
});
