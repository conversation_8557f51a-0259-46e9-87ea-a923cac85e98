using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.Mvc;
using Shared.Application.Exceptions;
using System.Text.Json;

namespace Core.Api.Infrastructure;

// cannot be moved to a shared project because it uses ASP.NET Core Web specific types
// changes made to this file should be most likely reflected in other API projects too!

public class GlobalExceptionHandler : IExceptionHandler
{
	private readonly Dictionary<Type, Func<HttpContext, Exception, Task>> _exceptionHandlers;
	private readonly ILogger<GlobalExceptionHandler> _logger;

	public GlobalExceptionHandler(ILogger<GlobalExceptionHandler> logger)
	{
		_logger = logger;
		_exceptionHandlers = new()
			{
				{ typeof(ValidationException), HandleValidationException },
				{ typeof(UnauthorizedAccessException), HandleUnauthorizedAccessException },
				{ typeof(NotFoundException), HandleNotFoundException },
				{ typeof(Refit.ApiException), HandleRefitApiException }
			};
	}

	public async ValueTask<bool> TryHandleAsync(HttpContext httpContext, Exception exception, CancellationToken cancellationToken)
	{
		// Handle specific exception types with exact type matching first
		var exceptionType = exception.GetType();
		if (_exceptionHandlers.TryGetValue(exceptionType, out Func<HttpContext, Exception, Task>? value))
		{
			await value.Invoke(httpContext, exception);
			return true;
		}

		// Handle exceptions by inheritance/interface (for cases like Refit.ApiException)
		foreach (var handlerPair in _exceptionHandlers)
		{
			if (handlerPair.Key.IsAssignableFrom(exceptionType))
			{
				await handlerPair.Value.Invoke(httpContext, exception);
				return true;
			}
		}

		_logger.LogError(exception, "Unhandled exception occurred.");

		return false;
	}

	private async Task HandleValidationException(HttpContext httpContext, Exception ex)
	{
		var exception = (ValidationException)ex;

		httpContext.Response.StatusCode = StatusCodes.Status400BadRequest;

		_logger.LogWarning(exception, "Validation failed with errors: {Errors}", exception.Errors);

		await httpContext.Response.WriteAsJsonAsync(new ValidationProblemDetails(exception.Errors)
		{
			Status = StatusCodes.Status400BadRequest,
		});
	}

	private async Task HandleUnauthorizedAccessException(HttpContext httpContext, Exception ex)
	{
		httpContext.Response.StatusCode = StatusCodes.Status401Unauthorized;

		_logger.LogWarning(ex, "Unauthorized access attempt.");

		await httpContext.Response.WriteAsJsonAsync(new ProblemDetails
		{
			Status = StatusCodes.Status401Unauthorized,
			Title = "Unauthorized",
		});
	}

	private async Task HandleNotFoundException(HttpContext httpContext, Exception ex)
	{
		httpContext.Response.StatusCode = StatusCodes.Status404NotFound;

		_logger.LogWarning(ex, "Entity not found exception.");

		await httpContext.Response.WriteAsJsonAsync(new ProblemDetails
		{
			Status = StatusCodes.Status404NotFound,
			Title = "Not Found",
			Detail = ex.Message
		});
	}

	private async Task HandleRefitApiException(HttpContext httpContext, Exception ex)
	{
		Refit.ApiException apiException = (Refit.ApiException)ex;
		httpContext.Response.StatusCode = (int)apiException.StatusCode;

		_logger.LogWarning(apiException, "API communication error: {StatusCode} {Content}",
			apiException.StatusCode, apiException.Content);

		// Try to parse the content from the downstream API
		var contentObject = GetObjectFromJsonString(apiException.Content);

		// If the downstream API returned structured error data, use it
		if (contentObject is JsonElement jsonElement && jsonElement.ValueKind == JsonValueKind.Object)
		{
			// Forward the structured error response from the downstream API
			await httpContext.Response.WriteAsJsonAsync(contentObject);
		}
		else
		{
			// Fallback to our own error structure
			await httpContext.Response.WriteAsJsonAsync(new
			{
				Status = (int)apiException.StatusCode,
				Title = GetTitleForStatusCode((int)apiException.StatusCode),
				Detail = apiException.Content ?? apiException.Message
			});
		}
	}

	private static object GetObjectFromJsonString(string? content)
	{
		if (string.IsNullOrWhiteSpace(content))
		{
			return new object();
		}

		try
		{
			return JsonSerializer.Deserialize<JsonElement>(content);
		}
		catch
		{
			// If deserialization fails, return the raw string as a fallback
			return content;
		}
	}

	private static string GetTitleForStatusCode(int statusCode)
	{
		return statusCode switch
		{
			400 => "Bad Request",
			401 => "Unauthorized",
			403 => "Forbidden",
			404 => "Not Found",
			409 => "Conflict",
			422 => "Unprocessable Entity",
			500 => "Internal Server Error",
			502 => "Bad Gateway",
			503 => "Service Unavailable",
			_ => "API Error"
		};
	}
}
