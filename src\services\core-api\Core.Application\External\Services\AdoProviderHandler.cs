using Core.Application.External.AdoApi;
using Core.Application.External.Interfaces;
using Shared.Application.Contracts.ConnectionDetails;

namespace Core.Application.External.Services;

public class AdoProviderHandler : IProviderHandler
{
	public string ServiceName => "ado";
	private readonly IAdoApiConnectionsClient _client;

	public AdoProviderHandler(IAdoApiConnectionsClient client)
	{
		_client = client;
	}

	public async Task<IProviderConnectionDetails> GetDetailsAsync(Guid coreConnectionId)
	{
		return await _client.GetConnectionDetailsAsync(coreConnectionId);
	}
}
