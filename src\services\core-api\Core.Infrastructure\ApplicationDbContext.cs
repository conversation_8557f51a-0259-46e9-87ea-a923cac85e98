using Core.Application.Common.Interfaces;
using Core.Domain.Connection;
using Core.Domain.ConnectionTypes;
using Microsoft.EntityFrameworkCore;
using System.Reflection;

namespace Core.Infrastructure;

public class ApplicationDbContext : DbContext, IApplicationDbContext
{
	public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
	{

	}

#if DEBUG
	public ApplicationDbContext() : base(new DbContextOptionsBuilder<ApplicationDbContext>()
		.UseNpgsql("Host=localhost:5433;Database=coredb;Username=coreuser;Password=corepassword")
		.Options)
	{
		// This constructor is for design-time (migrations) only AND SHOULDN'T BE CALLED BY THE APPLICATION.
	}
#endif

	public DbSet<Connection> Connections { get; set; }
	public DbSet<ConnectionType> ConnectionTypes { get; set; }

	protected override void OnModelCreating(ModelBuilder builder)
	{
		builder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());

		builder.HasDefaultSchema(Schemas.Default);
	}
}
