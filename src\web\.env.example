NUXT_SESSION_PASSWORD="" # random string, at least 32 characters long
NUXT_OAUTH_KEYCLOAK_CLIENT_ID="nuxt-app" #client ID of the Keycloak client
NUXT_OAUTH_KEYCLOAK_CLIENT_SECRET="" # client secret of the Keycloak client
NUXT_OAUTH_KEYCLOAK_SERVER_URL="http://localhost:8080" # Keycloak server URL
NUXT_OAUTH_KEYCLOAK_REALM="tmerge" # Keycloak realm name
NUXT_OAUTH_KEYCLOAK_SERVER_URL_INTERNAL="http://keycloak:8080" # internal Keycloak server URL, used in Docker Compose
CORE_API_URL="http://localhost:8081" # URL of the core API service (internal or external)
ADO_API_URL="http://localhost:8082" # URL of the ADO API service (internal or external)
BASE_URL="http://localhost:3000" # Base URL of the Nuxt application
