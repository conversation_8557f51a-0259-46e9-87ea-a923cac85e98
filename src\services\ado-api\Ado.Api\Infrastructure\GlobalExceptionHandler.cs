using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.Mvc;
using Microsoft.VisualStudio.Services.Common;
using Microsoft.VisualStudio.Services.WebApi;
using Shared.Application.Exceptions;

namespace Ado.Api.Infrastructure;

// cannot be moved to a shared project because it uses ASP.NET Core Web specific types
// changes made to this file should be most likely reflected in other API projects too!

public class GlobalExceptionHandler : IExceptionHandler
{
	private readonly Dictionary<Type, Func<HttpContext, Exception, Task>> _exceptionHandlers;
	private readonly ILogger<GlobalExceptionHandler> _logger;

	public GlobalExceptionHandler(ILogger<GlobalExceptionHandler> logger)
	{
		_logger = logger;
		_exceptionHandlers = new()
			{
				{ typeof(ValidationException), HandleValidationException },
				{ typeof(UnauthorizedAccessException), HandleUnauthorizedAccessException },
				{ typeof(VssUnauthorizedException), HandleVssUnauthorizedAccessException },
				{ typeof(VssServiceResponseException), HandleVssServiceResponseException },
				{ typeof(NotFoundException), HandleNotFoundException }
			};
	}

	public async ValueTask<bool> TryHandleAsync(HttpContext httpContext, Exception exception, CancellationToken cancellationToken)
	{
		// Handle specific exception types with exact type matching first
		var exceptionType = exception.GetType();
		if (_exceptionHandlers.TryGetValue(exceptionType, out Func<HttpContext, Exception, Task>? value))
		{
			await value.Invoke(httpContext, exception);
			return true;
		}

		// Handle exceptions by inheritance/interface
		foreach (var handlerPair in _exceptionHandlers)
		{
			if (handlerPair.Key.IsAssignableFrom(exceptionType))
			{
				await handlerPair.Value.Invoke(httpContext, exception);
				return true;
			}
		}

		_logger.LogError(exception, "Unhandled exception occurred.");

		return false;
	}

	private async Task HandleValidationException(HttpContext httpContext, Exception ex)
	{
		var exception = (ValidationException)ex;

		httpContext.Response.StatusCode = StatusCodes.Status400BadRequest;

		_logger.LogWarning(exception, "Validation failed with errors: {Errors}", exception.Errors);

		await httpContext.Response.WriteAsJsonAsync(new ValidationProblemDetails(exception.Errors)
		{
			Status = StatusCodes.Status400BadRequest,
		});
	}

	private async Task HandleUnauthorizedAccessException(HttpContext httpContext, Exception ex)
	{
		httpContext.Response.StatusCode = StatusCodes.Status401Unauthorized;

		_logger.LogWarning(ex, "Unauthorized access attempt.");

		await httpContext.Response.WriteAsJsonAsync(new ProblemDetails
		{
			Status = StatusCodes.Status401Unauthorized,
			Title = "Unauthorized",
		});
	}

	private async Task HandleVssUnauthorizedAccessException(HttpContext httpContext, Exception ex)
	{
		httpContext.Response.StatusCode = StatusCodes.Status400BadRequest;

		_logger.LogWarning(ex, "Couldn't reach VSS APIs - PAT isn't authorized or insufficient permissions.");

		await httpContext.Response.WriteAsJsonAsync(new ProblemDetails
		{
			Status = StatusCodes.Status400BadRequest,
			Title = "Authentication Failed",
			Detail = "The Personal Access Token (PAT) is invalid or doesn't have sufficient permissions to access Azure DevOps."
		});
	}

	private async Task HandleVssServiceResponseException(HttpContext httpContext, Exception ex)
	{
		httpContext.Response.StatusCode = StatusCodes.Status400BadRequest;

		_logger.LogWarning(ex, "The ADO API returned an error response. This might happen if the service instance URL is incorrect or cannot be found.");

		await httpContext.Response.WriteAsJsonAsync(new ProblemDetails
		{
			Status = StatusCodes.Status400BadRequest,
			Title = "Azure DevOps Connection Failed",
			Detail = "Unable to connect to the Azure DevOps instance. Please verify the URL is correct and accessible."
		});
	}

	private async Task HandleNotFoundException(HttpContext httpContext, Exception ex)
	{
		httpContext.Response.StatusCode = StatusCodes.Status404NotFound;

		await httpContext.Response.WriteAsJsonAsync(new ProblemDetails
		{
			Status = StatusCodes.Status404NotFound,
			Title = "Entity Not Found",
			Detail = ex.Message
		});
	}
}
