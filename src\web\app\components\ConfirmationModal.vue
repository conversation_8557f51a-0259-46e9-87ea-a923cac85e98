<script setup lang="ts">
	defineProps<{
		title: string;
		message: string;
		cancelText?: string;
		confirmText?: string;
		confirmButtonColor?: "primary" | "neutral" | "secondary" | "success" | "info" | "warning" | "error";
	}>();

	const emit = defineEmits(["close", "confirm"]);

	const handleCancel = () => {
		emit("close");
	};

	const handleConfirm = () => {
		emit("confirm");
		emit("close");
	};
</script>

<template>
	<UModal :close="false">
		<template #body>
			<div class="p-4">
				<h3 class="text-xl font-semibold">{{ title }}</h3>
				<p class="my-4">{{ message }}</p>
				<div class="flex justify-end space-x-2">
					<UButton class="rounded-full" size="xl" @click="handleCancel" variant="outline">{{ cancelText }}</UButton>
					<UButton class="rounded-full" size="xl" :color="confirmButtonColor || 'primary'" @click="handleConfirm" variant="solid">{{ confirmText }}</UButton>
				</div>
			</div>
		</template>
	</UModal>
</template>
