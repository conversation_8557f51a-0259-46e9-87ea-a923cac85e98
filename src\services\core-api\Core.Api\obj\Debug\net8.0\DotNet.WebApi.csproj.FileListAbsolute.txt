C:/Work/github/microservices-example/src/dotnet-api/DotNet.WebApi/DotNet.WebApi/bin/Debug/net8.0/appsettings.Development.json
C:/Work/github/microservices-example/src/dotnet-api/DotNet.WebApi/DotNet.WebApi/bin/Debug/net8.0/appsettings.json
C:/Work/github/microservices-example/src/dotnet-api/DotNet.WebApi/DotNet.WebApi/bin/Debug/net8.0/DotNet.WebApi.exe
C:/Work/github/microservices-example/src/dotnet-api/DotNet.WebApi/DotNet.WebApi/bin/Debug/net8.0/DotNet.WebApi.deps.json
C:/Work/github/microservices-example/src/dotnet-api/DotNet.WebApi/DotNet.WebApi/bin/Debug/net8.0/DotNet.WebApi.runtimeconfig.json
C:/Work/github/microservices-example/src/dotnet-api/DotNet.WebApi/DotNet.WebApi/bin/Debug/net8.0/DotNet.WebApi.dll
C:/Work/github/microservices-example/src/dotnet-api/DotNet.WebApi/DotNet.WebApi/bin/Debug/net8.0/DotNet.WebApi.pdb
C:/Work/github/microservices-example/src/dotnet-api/DotNet.WebApi/DotNet.WebApi/bin/Debug/net8.0/Microsoft.OpenApi.dll
C:/Work/github/microservices-example/src/dotnet-api/DotNet.WebApi/DotNet.WebApi/bin/Debug/net8.0/Swashbuckle.AspNetCore.Swagger.dll
C:/Work/github/microservices-example/src/dotnet-api/DotNet.WebApi/DotNet.WebApi/bin/Debug/net8.0/Swashbuckle.AspNetCore.SwaggerGen.dll
C:/Work/github/microservices-example/src/dotnet-api/DotNet.WebApi/DotNet.WebApi/bin/Debug/net8.0/Swashbuckle.AspNetCore.SwaggerUI.dll
C:/Work/github/microservices-example/src/dotnet-api/DotNet.WebApi/DotNet.WebApi/obj/Debug/net8.0/DotNet.WebApi.csproj.AssemblyReference.cache
C:/Work/github/microservices-example/src/dotnet-api/DotNet.WebApi/DotNet.WebApi/obj/Debug/net8.0/DotNet.WebApi.GeneratedMSBuildEditorConfig.editorconfig
C:/Work/github/microservices-example/src/dotnet-api/DotNet.WebApi/DotNet.WebApi/obj/Debug/net8.0/DotNet.WebApi.AssemblyInfoInputs.cache
C:/Work/github/microservices-example/src/dotnet-api/DotNet.WebApi/DotNet.WebApi/obj/Debug/net8.0/DotNet.WebApi.AssemblyInfo.cs
C:/Work/github/microservices-example/src/dotnet-api/DotNet.WebApi/DotNet.WebApi/obj/Debug/net8.0/DotNet.WebApi.csproj.CoreCompileInputs.cache
C:/Work/github/microservices-example/src/dotnet-api/DotNet.WebApi/DotNet.WebApi/obj/Debug/net8.0/DotNet.WebApi.MvcApplicationPartsAssemblyInfo.cs
C:/Work/github/microservices-example/src/dotnet-api/DotNet.WebApi/DotNet.WebApi/obj/Debug/net8.0/DotNet.WebApi.MvcApplicationPartsAssemblyInfo.cache
C:/Work/github/microservices-example/src/dotnet-api/DotNet.WebApi/DotNet.WebApi/obj/Debug/net8.0/DotNet.WebApi.sourcelink.json
C:/Work/github/microservices-example/src/dotnet-api/DotNet.WebApi/DotNet.WebApi/obj/Debug/net8.0/staticwebassets.build.json
C:/Work/github/microservices-example/src/dotnet-api/DotNet.WebApi/DotNet.WebApi/obj/Debug/net8.0/staticwebassets.development.json
C:/Work/github/microservices-example/src/dotnet-api/DotNet.WebApi/DotNet.WebApi/obj/Debug/net8.0/staticwebassets/msbuild.DotNet.WebApi.Microsoft.AspNetCore.StaticWebAssets.props
C:/Work/github/microservices-example/src/dotnet-api/DotNet.WebApi/DotNet.WebApi/obj/Debug/net8.0/staticwebassets/msbuild.build.DotNet.WebApi.props
C:/Work/github/microservices-example/src/dotnet-api/DotNet.WebApi/DotNet.WebApi/obj/Debug/net8.0/staticwebassets/msbuild.buildMultiTargeting.DotNet.WebApi.props
C:/Work/github/microservices-example/src/dotnet-api/DotNet.WebApi/DotNet.WebApi/obj/Debug/net8.0/staticwebassets/msbuild.buildTransitive.DotNet.WebApi.props
C:/Work/github/microservices-example/src/dotnet-api/DotNet.WebApi/DotNet.WebApi/obj/Debug/net8.0/staticwebassets.pack.json
C:/Work/github/microservices-example/src/dotnet-api/DotNet.WebApi/DotNet.WebApi/obj/Debug/net8.0/scopedcss/bundle/DotNet.WebApi.styles.css
C:/Work/github/microservices-example/src/dotnet-api/DotNet.WebApi/DotNet.WebApi/obj/Debug/net8.0/DotNet.W.A6AD919F.Up2Date
C:/Work/github/microservices-example/src/dotnet-api/DotNet.WebApi/DotNet.WebApi/obj/Debug/net8.0/DotNet.WebApi.dll
C:/Work/github/microservices-example/src/dotnet-api/DotNet.WebApi/DotNet.WebApi/obj/Debug/net8.0/refint/DotNet.WebApi.dll
C:/Work/github/microservices-example/src/dotnet-api/DotNet.WebApi/DotNet.WebApi/obj/Debug/net8.0/DotNet.WebApi.pdb
C:/Work/github/microservices-example/src/dotnet-api/DotNet.WebApi/DotNet.WebApi/obj/Debug/net8.0/DotNet.WebApi.genruntimeconfig.cache
C:/Work/github/microservices-example/src/dotnet-api/DotNet.WebApi/DotNet.WebApi/obj/Debug/net8.0/ref/DotNet.WebApi.dll
/app/DotNet.WebApi/bin/Debug/net8.0/appsettings.Development.json
/app/DotNet.WebApi/bin/Debug/net8.0/appsettings.json
/app/DotNet.WebApi/bin/Debug/net8.0/DotNet.WebApi
/app/DotNet.WebApi/bin/Debug/net8.0/DotNet.WebApi.deps.json
/app/DotNet.WebApi/bin/Debug/net8.0/DotNet.WebApi.runtimeconfig.json
/app/DotNet.WebApi/bin/Debug/net8.0/DotNet.WebApi.dll
/app/DotNet.WebApi/bin/Debug/net8.0/DotNet.WebApi.pdb
/app/DotNet.WebApi/bin/Debug/net8.0/Microsoft.OpenApi.dll
/app/DotNet.WebApi/bin/Debug/net8.0/Swashbuckle.AspNetCore.Swagger.dll
/app/DotNet.WebApi/bin/Debug/net8.0/Swashbuckle.AspNetCore.SwaggerGen.dll
/app/DotNet.WebApi/bin/Debug/net8.0/Swashbuckle.AspNetCore.SwaggerUI.dll
/app/DotNet.WebApi/obj/Debug/net8.0/DotNet.WebApi.csproj.AssemblyReference.cache
/app/DotNet.WebApi/obj/Debug/net8.0/DotNet.WebApi.GeneratedMSBuildEditorConfig.editorconfig
/app/DotNet.WebApi/obj/Debug/net8.0/DotNet.WebApi.AssemblyInfoInputs.cache
/app/DotNet.WebApi/obj/Debug/net8.0/DotNet.WebApi.AssemblyInfo.cs
/app/DotNet.WebApi/obj/Debug/net8.0/DotNet.WebApi.csproj.CoreCompileInputs.cache
/app/DotNet.WebApi/obj/Debug/net8.0/DotNet.WebApi.MvcApplicationPartsAssemblyInfo.cs
/app/DotNet.WebApi/obj/Debug/net8.0/DotNet.WebApi.MvcApplicationPartsAssemblyInfo.cache
/app/DotNet.WebApi/obj/Debug/net8.0/staticwebassets.build.json
/app/DotNet.WebApi/obj/Debug/net8.0/staticwebassets.development.json
/app/DotNet.WebApi/obj/Debug/net8.0/staticwebassets/msbuild.DotNet.WebApi.Microsoft.AspNetCore.StaticWebAssets.props
/app/DotNet.WebApi/obj/Debug/net8.0/staticwebassets/msbuild.build.DotNet.WebApi.props
/app/DotNet.WebApi/obj/Debug/net8.0/staticwebassets/msbuild.buildMultiTargeting.DotNet.WebApi.props
/app/DotNet.WebApi/obj/Debug/net8.0/staticwebassets/msbuild.buildTransitive.DotNet.WebApi.props
/app/DotNet.WebApi/obj/Debug/net8.0/staticwebassets.pack.json
/app/DotNet.WebApi/obj/Debug/net8.0/scopedcss/bundle/DotNet.WebApi.styles.css
/app/DotNet.WebApi/obj/Debug/net8.0/DotNet.W.A6AD919F.Up2Date
/app/DotNet.WebApi/obj/Debug/net8.0/DotNet.WebApi.dll
/app/DotNet.WebApi/obj/Debug/net8.0/refint/DotNet.WebApi.dll
/app/DotNet.WebApi/obj/Debug/net8.0/DotNet.WebApi.pdb
/app/DotNet.WebApi/obj/Debug/net8.0/DotNet.WebApi.genruntimeconfig.cache
/app/DotNet.WebApi/obj/Debug/net8.0/ref/DotNet.WebApi.dll
