http:
    routers:
        # Web application
        web-router:
            rule: "Host(`tmerge.localhost`)"
            service: web-service
            priority: 1

        # Keycloak
        keycloak-router:
            rule: "(Host(`keycloak.localhost`))"
            service: keycloak-service
            priority: 10

        # Grafana
        grafana-router:
            rule: "(Host(`grafana.localhost`))"
            service: grafana-service
            priority: 20

        # Traefik
        traefik-router:
            rule: "(Host(`traefik.localhost`))"
            service: traefik-service
            priority: 30

    services:
        web-service:
            loadBalancer:
                servers:
                    - url: "http://web:3000"

        keycloak-service:
            loadBalancer:
                servers:
                    - url: "http://keycloak:8080"

        grafana-service:
            loadBalancer:
                servers:
                    - url: "http://grafana:3000"

        traefik-service:
            loadBalancer:
                servers:
                    - url: "http://traefik:8080"
