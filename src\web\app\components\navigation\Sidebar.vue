<script setup lang="ts">
	import type { NavigationMenuItem } from "@nuxt/ui";

	const { user } = useUserSession();
	const { links } = await useNavigationLinks();
</script>

<template>
	<div class="flex flex-col w-80 border-r border-gray-200 dark:border-gray-800 h-full p-4">
		<p class="mb-6 text-center text-2xl font-bold text-primary">tmerge</p>

		<!-- Navigation -->
		<UNavigationMenu orientation="vertical" :items="links" class="flex-1">
			<template #user-leading="{ item }: { item: NavigationMenuItem }">
				<UAvatar size="sm" icon="i-heroicons-user" :src="item.value || ''" />
			</template>
		</UNavigationMenu>

		<!-- User Popover -->
		<div class="flex items-center">
			<NavigationProfileSettings :align="'start'" side="top" />
			<p class="ml-4">{{ user?.name }}</p>
		</div>
	</div>
</template>
