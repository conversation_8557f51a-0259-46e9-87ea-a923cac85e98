using Core.Application.ConnectionTypes.Queries;
using Core.Domain.ConnectionTypes;
using Microsoft.AspNetCore.Mvc;

namespace Core.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
public class ConnectionTypesController(ILogger<ConnectionTypesController> logger, ISender mediator) : ControllerBase
{
	private readonly ILogger<ConnectionTypesController> _logger = logger;
	private readonly ISender _mediator = mediator;

	[HttpGet()]
	public async Task<List<ConnectionType>> Get()
	{
		_logger.LogInformation("Fetching all connection types from the Core database.");
		var connectionTypes = await _mediator.Send(new GetConnectionTypesQuery());

		return connectionTypes;
	}
}
