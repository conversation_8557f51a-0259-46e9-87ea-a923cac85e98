using Ado.Api.Extensions;
using Ado.Application;
using Ado.Infrastructure;
using Serilog;
using Serilog.Sinks.Grafana.Loki;

namespace Ado.Api;

internal static class Program
{
    public static void Main(string[] args)
    {
        var builder = WebApplication.CreateBuilder(args);

        Log.Logger = new LoggerConfiguration()
            .MinimumLevel.Information()
            .Enrich.FromLogContext()
            .Enrich.WithProperty("service_name", "ado-api")
            .WriteTo.Console()
            // Fix: Use object initializer to set properties of LokiLabel  
            .WriteTo.GrafanaLoki("http://loki:3100", labels: [new LokiLabel { Key = "service_name", Value = "ado-api" }])
            .CreateLogger();

        builder.Services.AddSerilog();

        builder.Services
            .AddApplication()
            .AddPresentation()
            .AddInfrastructure(builder.Configuration);

        var app = builder.Build();

        // Configure the HTTP request pipeline.
        if (app.Environment.IsDevelopment())
        {
            app.UseSwagger();
            app.UseSwaggerUI();
        }

        app.UseSerilogRequestLogging();

        app.UseExceptionHandler();

        app.ApplyMigrations();

        app.UseHttpsRedirection();

        //app.UseAuthorization();

        app.MapControllers();

        app.Run();
    }
}
