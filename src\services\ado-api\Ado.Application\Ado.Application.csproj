﻿<Project Sdk="Microsoft.NET.Sdk">
	<ItemGroup>
		<PackageReference Include="FluentValidation.DependencyInjectionExtensions" />
		<PackageReference Include="MediatR" />
		<PackageReference Include="Microsoft.TeamFoundationServer.Client" />
		<PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" />
	</ItemGroup>


	<ItemGroup>
		<ProjectReference Include="..\Ado.Domain\Ado.Domain.csproj" />
		<ProjectReference Include="..\..\shared\Shared.Application\Shared.Application.csproj" />
	</ItemGroup>


	<ItemGroup>
	  <Folder Include="Common\Exceptions\" />
	</ItemGroup>

</Project>
