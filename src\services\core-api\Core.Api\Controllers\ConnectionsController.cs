using Core.Application.Connections.Commands.CreateConnection;
using Core.Application.Connections.Commands.DeleteConnection;
using Core.Application.Connections.Queries;
using Microsoft.AspNetCore.Mvc;
using Connection = Core.Domain.Connection.Connection;

namespace Core.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
public class ConnectionsController(ILogger<ConnectionsController> logger, ISender mediator) : ControllerBase
{
    private readonly ILogger<ConnectionsController> _logger = logger;
    private readonly ISender _mediator = mediator;

    [HttpGet]
    public async Task<List<Connection>> Get()
    {
        _logger.LogInformation("Fetching all connections from the Core database.");
        var connections = await _mediator.Send(new GetConnectionsQuery());

        return connections;
    }

    [HttpGet("{id:guid}")]
    public async Task<ConnectionDetailsResponse> GetById(Guid id)
    {
        _logger.LogInformation("Fetching connection details with ID: {ConnectionId}", id);
        var connectionDetails = await _mediator.Send(new GetConnectionDetailsQuery(id));

        return connectionDetails;
    }

    [HttpPost]
    public async Task<Connection> Post([FromBody] CreateConnectionCommand command)
    {
        _logger.LogInformation("Creating a new connection.");
        var connection = await _mediator.Send(command);

        return connection;
    }

    [HttpDelete("{id}")]
    public async Task<Connection> Delete(Guid id)
    {
        _logger.LogInformation("Deleting connection with ID: '{Id}'", id);
        var connection = await _mediator.Send(new DeleteConnectionCommand() { ConnectionId = id });

        return connection;
    }
}
