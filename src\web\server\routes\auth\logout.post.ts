export default defineEventHandler(async (event) => {
	try {
		const session = await getUserSession(event);
		const serverUrl = process.env.NUXT_OAUTH_KEYCLOAK_SERVER_URL || "http://localhost:8080";
		const realm = process.env.NUXT_OAUTH_KEYCLOAK_REALM || "tmerge";
		const baseUrl = process.env.BASE_URL || "http://localhost:3000";
		const redirectUri = encodeURIComponent(`${baseUrl}`);

		// Prepare the Keycloak logout URL
		const keycloakLogoutEndpoint = `${serverUrl}/realms/${realm}/protocol/openid-connect/logout`;
		const logoutUrl = `${keycloakLogoutEndpoint}?post_logout_redirect_uri=${redirectUri}&id_token_hint=${session.idToken}`;

		// clear the session with the nuxt-auth-utils method
		await clearUserSession(event);

		return { logoutUrl }
	} catch (error) {
		console.error("Failed to log out. Error: ", error);
		return error;
	}
});
