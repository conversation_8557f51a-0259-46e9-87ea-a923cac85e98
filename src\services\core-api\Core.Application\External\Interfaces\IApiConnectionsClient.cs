using Core.Application.Connections.Commands.CreateConnection;
using Refit;

namespace Core.Application.External.Interfaces;

public interface IApiConnectionsClient
{
	[Get("/api/connections")]
	Task<string> GetConnectionsAsync();

	[Get("/api/connections/{coreConnectionId}")]
	Task<string> GetConnectionDetailsAsync(Guid coreConnectionId);

	[Post("/api/connections")]
	Task<string> CreateConnectionAsync(CreateConnectionExternalDto request);
}
