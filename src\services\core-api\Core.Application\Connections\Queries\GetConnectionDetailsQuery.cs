using Core.Application.Common.Interfaces;
using Core.Application.External.Interfaces;
using Core.Domain.Connection;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Shared.Application.Contracts.ConnectionDetails;
using Shared.Application.Exceptions;

namespace Core.Application.Connections.Queries;

public record GetConnectionDetailsQuery(Guid Id) : IRequest<ConnectionDetailsResponse>;

public class GetConnectionDetailsQueryHandler(
	IApplicationDbContext context,
	IExternalServiceResolver serviceResolver,
	ILogger<GetConnectionDetailsQueryHandler> logger) : IRequestHandler<GetConnectionDetailsQuery, ConnectionDetailsResponse>
{
	private readonly IApplicationDbContext _context = context;
	private readonly IExternalServiceResolver _serviceResolver = serviceResolver;
	private readonly ILogger<GetConnectionDetailsQueryHandler> _logger = logger;

	public async Task<ConnectionDetailsResponse> Handle(GetConnectionDetailsQuery request, CancellationToken cancellationToken)
	{
		// Get the core connection information
		var connection = await _context.Connections
			.Where(x => x.Id == request.Id && x.IsDeleted == false)
			.Include(x => x.ConnectionType)
			.FirstOrDefaultAsync(cancellationToken);

		if (connection == null)
			throw new NotFoundException($"Connection with ID '{request.Id}' was not found.");

		_logger.LogInformation("Fetching connection details for connection: {ConnectionId} of type: {ServiceName}",
			request.Id, connection.ConnectionType.ServiceName);

		IProviderConnectionDetails? providerDetails = null;
		try
		{
			var handler = _serviceResolver.GetHandler(connection.ConnectionType.ServiceName);
			providerDetails = await handler.GetDetailsAsync(connection.Id);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Failed to retrieve provider details for connection {ConnectionId}. Returning core details only.", request.Id);
		}

		return new ConnectionDetailsResponse
		{
			Id = connection.Id,
			Name = connection.Name,
			Description = connection.Description,
			ConnectionType = connection.ConnectionType,
			ProviderDetails = providerDetails,
			CreatedAt = connection.CreatedAt,
			LastUpdatedAt = connection.LastUpdatedAt
		};
	}
}

public class ConnectionDetailsResponse : Connection
{
	public dynamic? ProviderDetails { get; set; }
}
