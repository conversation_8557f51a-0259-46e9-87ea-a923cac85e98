using Core.Application.Connections.Commands.CreateConnection;
using Refit;
using Shared.Application.Contracts.ConnectionDetails;

namespace Core.Application.External.AdoApi;

public interface IAdoApiConnectionsClient
{
	[Get("/api/connections")]
	Task<string> GetConnectionsAsync();

	[Get("/api/connections/{coreConnectionId}")]
	Task<AzureDevOpsConnectionDetails> GetConnectionDetailsAsync(Guid coreConnectionId);

	[Post("/api/connections")]
	Task<string> CreateConnectionAsync(CreateConnectionExternalDto request);
}
