{"name": "tmerge-web", "version": "0.0.1", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@internationalized/date": "3.8.0", "@nuxt/ui": "3.1.2", "@pinia/nuxt": "0.11.0", "@vueuse/nuxt": "13.2.0", "nuxt": "3.17.3", "nuxt-auth-utils": "0.5.20", "pinia": "3.0.2", "vue": "3.5.14", "vue-router": "4.5.1", "zod": "3.24.4"}, "devDependencies": {"@iconify-json/heroicons": "1.2.2"}}