# Copilot Instructions for tmerge

## Project Architecture

**tmerge** is a microservices application integrating Azure DevOps and other ALM tools. It uses:

- **Backend**: .NET 9 microservices with clean architecture (Core API + ADO API)
- **Frontend**: Nuxt 3 with Nuxt Layers architecture, NuxtUI, Pinia, TypeScript
- **Infrastructure**: Docker Compose, PostgreSQL databases, Keycloak auth, observability stack
- **Development**: Hot-reload enabled containers with volume mounts

## Key Development Workflows

### Starting Development Environment
```powershell
.\scripts\start-dev.ps1  # Starts all Docker services
cd src\web && pnpm dev  # Start frontend separately for hot-reload
```

**Service Endpoints**: Core API (8081), ADO API (8082), Keycloak (8080), Grafana (5341), Web (3000)

### Backend Development
- Each API is a separate microservice with its own PostgreSQL database
- Uses **CQRS + MediatR** pattern with Commands/Queries in Application layer
- **Entity Framework Core** with code-first migrations
- **Refit** for inter-service communication via `IExternalServiceResolver`
- Shared business logic in `shared/` projects using Directory.Build.props/Directory.Packages.props

### Frontend Development
- **Nuxt Layers** architecture: `layers/connections/`, `layers/dashboard/`
- Each layer has its own components, stores, pages, and types
- **Convention**: Connection forms in `layers/connections/app/components/connection-forms/`
- **API Proxy**: Frontend calls backend via `/api/core?url=<endpoint>` proxy pattern

## Critical Patterns

### Connection Form Components
Follow the `AzureDevOpsConnectionForm.vue` pattern:
- Separate form data and API payload interfaces
- Zod validation with provider-specific URL validation
- Emit `update:data` and `update:valid` events
- Export interfaces for type safety across components

### Backend Command/Query Structure
```cs
// Command pattern
public record CreateConnectionCommand : IRequest<Connection>
public class CreateConnectionCommandHandler : IRequestHandler<CreateConnectionCommand, Connection>

// External service integration
IApiConnectionsClient externalClient = _serviceResolver.ResolveConnectionsClient(serviceName);
```

### Docker Development Setup
- **Dev containers**: Use `Dockerfile.dev` with `dotnet watch` and volume mounts
- **Production containers**: Use standard `Dockerfile` with optimized builds
- **Hot-reload**: Source code mounted at `/app` with `/bin/` and `/obj/` excluded

### Connection Type Registry
- New connection types must be registered in `CONNECTION_TYPE_COMPONENTS` mapping
- Backend maps `serviceName` to API client via `IExternalServiceResolver`
- Database seeding defines available connection types

## Database Conventions
- **Core DB** (port 5433): Connection metadata, connection types
- **ADO DB** (port 5434): Azure DevOps-specific connection data
- Each microservice owns its domain data with `CoreConnectionId` as foreign key

## Authentication & Authorization
- **Keycloak** integration via `nuxt-auth-utils`
- Global auth middleware redirects unauthenticated users to `/auth/keycloak`
- Session management with encrypted cookies

## Adding New Connection Providers
1. Create form component in `layers/connections/app/components/connection-forms/`
2. Add to `CONNECTION_TYPE_COMPONENTS` mapping and type definitions
3. Create new microservice API or extend existing one
4. Register client in `IExternalServiceResolver`
5. Seed connection type in database

## Observability
- **OpenTelemetry** collector routes logs/metrics/traces to Grafana stack
- **Prometheus** for metrics, **Loki** for logs, **Tempo** for traces
- Access Grafana dashboards at http://localhost:5341 in development
