name: "tmerge-dev"

services:
  core-api:
    image: tmerge-dev/core-api
    build:
      context: ../../src/services
      dockerfile: ./core-api/Dockerfile.dev
    ports:
      - 8081:8081 # must be exposed for debugging
    depends_on:
      - postgres-core
    volumes:
      - ../../src/services:/app
      - /app/core-api/Core.Api/bin/
      - /app/core-api/Core.Api/obj/
      - /app/core-api/Core.Application/bin/
      - /app/core-api/Core.Application/obj/
      - /app/core-api/Core.Domain/bin/
      - /app/core-api/Core.Domain/obj/
      - /app/core-api/Core.Infrastructure/bin/
      - /app/core-api/Core.Infrastructure/obj/
      - /app/shared/Shared.Application/bin/
      - /app/shared/Shared.Application/obj/
      - /app/shared/Shared.Domain/bin/
      - /app/shared/Shared.Domain/obj/

  ado-api:
    image: tmerge-dev/ado-api
    build:
      context: ../../src/services
      dockerfile: ./ado-api/Dockerfile.dev
    ports:
      - 8082:8082 # must be exposed for debugging
    depends_on:
      - postgres-core
    volumes:
      - ../../src/services:/app
      - /app/ado-api/Ado.Api/bin/
      - /app/ado-api/Ado.Api/obj/
      - /app/ado-api/Ado.Application/bin/
      - /app/ado-api/Ado.Application/obj/
      - /app/ado-api/Ado.Domain/bin/
      - /app/ado-api/Ado.Domain/obj/
      - /app/ado-api/Ado.Infrastructure/bin/
      - /app/ado-api/Ado.Infrastructure/obj/
      - /app/shared/Shared.Application/bin/
      - /app/shared/Shared.Application/obj/
      - /app/shared/Shared.Domain/bin/
      - /app/shared/Shared.Domain/obj/

  postgres-core:
    image: postgres:15
    environment:
      POSTGRES_DB: coredb
      POSTGRES_USER: coreuser
      POSTGRES_PASSWORD: corepassword
      PGPORT: 5432
    ports:
      - 5433:5432
    volumes:
      - postgres_core_data:/var/lib/postgresql/data

  postgres-ado:
    image: postgres:15
    environment:
      POSTGRES_DB: adodb
      POSTGRES_USER: adouser
      POSTGRES_PASSWORD: adopassword
      PGPORT: 5432
    ports:
      - 5434:5432
    volumes:
      - postgres_ado_data:/var/lib/postgresql/data

volumes:
  postgres_core_data:
  postgres_ado_data:
