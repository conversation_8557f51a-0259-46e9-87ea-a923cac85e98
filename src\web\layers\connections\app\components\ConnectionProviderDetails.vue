<script setup lang="ts">
	import AzureDevOpsConnectionDetails from "./connection-details/AzureDevOpsConnectionDetails.vue";

	// Props
	const props = defineProps<{
		connectionType: ConnectionTypeDetails;
		providerDetails: Record<string, any>;
	}>();

	// Check if this is Azure DevOps
	const isAzureDevOps = computed(() => {
		return props.connectionType.serviceName.toLowerCase() === "ado";
	});
</script>

<template>
	<div class="space-y-3">
		<!-- Azure DevOps Details -->
		<AzureDevOpsConnectionDetails v-if="isAzureDevOps" :connectionType="connectionType" :providerDetails="(providerDetails as AzureDevOpsProviderDetails)" />

		<!-- Generic details for other providers -->
		<div v-else class="space-y-3">
			<div v-for="(value, key) in providerDetails" :key="key" class="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-700">
				<span class="font-medium text-gray-600 dark:text-gray-400 capitalize"> {{ key.replace(/([A-Z])/g, " $1").trim() }}: </span>
				<span class="text-sm text-gray-900 dark:text-gray-100">
					{{ value }}
				</span>
			</div>
		</div>
	</div>
</template>
