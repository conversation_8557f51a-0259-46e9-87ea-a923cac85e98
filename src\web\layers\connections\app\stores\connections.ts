import { defineStore } from "pinia";

export type ConnectionsState = {
	connections: Connection[] | null;
	connectionDetails: Record<string, ConnectionType>;
	isLoading: boolean;
	isLoadingDetails: boolean;
	lastError: ProcessedError | null;
};

export interface CreateConnectionPayload {
	name: string;
	description?: string;
	connectionTypeId: string;
	payload: Record<string, any>;
}

export const useConnectionsStore = defineStore("connections", {
	state: () =>
	({
		connections: null,
		connectionDetails: {},
		isLoading: false,
		isLoadingDetails: false,
		lastError: null,
	} as ConnectionsState),
	actions: {
		async fetchConnections(): Promise<void> {
			const { handleError } = useApiError();

			try {
				this.isLoading = true;
				this.lastError = null;

				const { data } = await useFetch<Connection[]>("/api/core?url=connections");
				this.connections = data.value!;
			} catch (error) {
				console.error("Failed to fetch connections:", error);
				this.lastError = handleError(error, {
					context: { operation: 'fetchConnections', resource: 'connections' },
					showToast: false // Don't show toast for background operations
				});
			} finally {
				this.isLoading = false;
			}
		},

		async createConnection(payload: CreateConnectionPayload): Promise<Connection> {
			const { handleError } = useApiError();

			try {
				this.isLoading = true;
				this.lastError = null;

				const connection = await $fetch<Connection>("/api/core?url=connections", {
					method: 'POST',
					body: payload
				});

				// Refresh the connections list
				await this.fetchConnections();

				return connection;
			} catch (error) {
				console.error("Failed to create connection:", error);

				// Process the error but don't show toast here - let the component handle it
				this.lastError = handleError(error, {
					context: {
						operation: 'createConnection',
						resource: 'connections',
						userId: payload.name // Use connection name as identifier
					},
					showToast: false
				});

				// Re-throw the processed error for component handling
				throw this.lastError;
			} finally {
				this.isLoading = false;
			}
		},

		async fetchConnectionDetails(connectionId: string): Promise<ConnectionType> {
			const { handleError } = useApiError();

			try {
				this.isLoadingDetails = true;
				this.lastError = null;

				const { data } = await useFetch<ConnectionType>(`/api/core?url=connections/${connectionId}`);
				const connectionDetails = data.value!;

				// Cache the details
				this.connectionDetails[connectionId] = connectionDetails;

				return connectionDetails;
			} catch (error) {
				console.error("Failed to fetch connection details:", error);
				this.lastError = handleError(error, {
					context: { operation: 'fetchConnectionDetails', resource: 'connections', userId: connectionId },
					showToast: false // Don't show toast for background operations
				});

				// Re-throw the processed error for component handling
				throw this.lastError;
			} finally {
				this.isLoadingDetails = false;
			}
		},

		getConnectionDetails(connectionId: string): ConnectionType | null {
			return this.connectionDetails[connectionId] || null;
		},

		clearError() {
			this.lastError = null;
		}
	}
});
