name: "tmerge-prod"

services:
  grafana:
    image: grafana/grafana-oss:11.6.0
    volumes:
      - grafana_data:/var/lib/grafana
      - ../../src/grafana/provisioning:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
      - loki
      - tempo
      - otelcol

  loki:
    image: grafana/loki:3.4.4
    volumes:
      - loki_data:/loki

  tempo:
    image: grafana/tempo:2.6.1
    command: ["-config.file=/etc/tempo.yaml"]
    volumes:
      - tempo_data:/var/tempo
      - ../../src/grafana/tempo/tempo.yaml:/etc/tempo.yaml

  otelcol:
    image: otel/opentelemetry-collector-contrib:0.117.0
    command: ["--config=/etc/otelcol/otelcol.yaml"]
    volumes:
      - ../../src/grafana/otelcol/otelcol.yaml:/etc/otelcol/otelcol.yaml

  prometheus:
    image: prom/prometheus:v2.55.0
    command:
      - "--config.file=/etc/prometheus/prometheus.yml"
      - "--storage.tsdb.path=/prometheus"
      - "--web.console.libraries=/etc/prometheus/console_libraries"
      - "--web.console.templates=/etc/prometheus/consoles"
      - "--storage.tsdb.retention.time=200h"
      - "--web.enable-lifecycle"
      - "--web.enable-remote-write-receiver"
    volumes:
      - prometheus_data:/prometheus
      - ../../src/grafana/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml

volumes:
  grafana_data:
  loki_data:
  tempo_data:
  prometheus_data:
