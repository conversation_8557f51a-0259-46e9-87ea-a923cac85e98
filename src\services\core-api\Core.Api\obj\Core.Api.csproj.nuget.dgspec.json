{"format": 1, "restore": {"C:\\Work\\tme\\tmerge\\src\\services\\core-api\\Core.Api\\Core.Api.csproj": {}}, "projects": {"C:\\Work\\tme\\tmerge\\src\\services\\core-api\\Core.Api\\Core.Api.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Work\\tme\\tmerge\\src\\services\\core-api\\Core.Api\\Core.Api.csproj", "projectName": "Core.Api", "projectPath": "C:\\Work\\tme\\tmerge\\src\\services\\core-api\\Core.Api\\Core.Api.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Work\\tme\\tmerge\\src\\services\\core-api\\Core.Api\\obj\\", "projectStyle": "PackageReference", "centralPackageVersionsManagementEnabled": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Work\\tme\\tmerge\\src\\services\\core-api\\Core.Infrastructure\\Core.Infrastructure.csproj": {"projectPath": "C:\\Work\\tme\\tmerge\\src\\services\\core-api\\Core.Infrastructure\\Core.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"OpenTelemetry.Exporter.OpenTelemetryProtocol": {"target": "Package", "version": "[1.12.0, )", "versionCentrallyManaged": true}, "OpenTelemetry.Extensions.Hosting": {"target": "Package", "version": "[1.12.0, )", "versionCentrallyManaged": true}, "OpenTelemetry.Instrumentation.AspNetCore": {"target": "Package", "version": "[1.12.0, )", "versionCentrallyManaged": true}, "OpenTelemetry.Instrumentation.Http": {"target": "Package", "version": "[1.12.0, )", "versionCentrallyManaged": true}, "OpenTelemetry.Instrumentation.Runtime": {"target": "Package", "version": "[1.12.0, )", "versionCentrallyManaged": true}, "Serilog.AspNetCore": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "Serilog.Sinks.Grafana.Loki": {"target": "Package", "version": "[8.3.1, )", "versionCentrallyManaged": true}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[7.2.0, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"FluentValidation.DependencyInjectionExtensions": "11.11.0", "MediatR": "12.4.1", "Microsoft.EntityFrameworkCore": "9.0.1", "Microsoft.EntityFrameworkCore.Design": "9.0.1", "Microsoft.EntityFrameworkCore.Tools": "9.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.1", "Microsoft.TeamFoundationServer.Client": "19.225.1", "Newtonsoft.Json": "13.0.3", "Npgsql.EntityFrameworkCore.PostgreSQL": "9.0.3", "OpenTelemetry.Exporter.OpenTelemetryProtocol": "1.12.0", "OpenTelemetry.Extensions.Hosting": "1.12.0", "OpenTelemetry.Instrumentation.AspNetCore": "1.12.0", "OpenTelemetry.Instrumentation.Http": "1.12.0", "OpenTelemetry.Instrumentation.Runtime": "1.12.0", "Refit": "8.0.0", "Refit.HttpClientFactory": "8.0.0", "Serilog": "4.2.0", "Serilog.AspNetCore": "9.0.0", "Serilog.Sinks.Grafana.Loki": "8.3.1", "Swashbuckle.AspNetCore": "7.2.0"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Work\\tme\\tmerge\\src\\services\\core-api\\Core.Application\\Core.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Work\\tme\\tmerge\\src\\services\\core-api\\Core.Application\\Core.Application.csproj", "projectName": "Core.Application", "projectPath": "C:\\Work\\tme\\tmerge\\src\\services\\core-api\\Core.Application\\Core.Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Work\\tme\\tmerge\\src\\services\\core-api\\Core.Application\\obj\\", "projectStyle": "PackageReference", "centralPackageVersionsManagementEnabled": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Work\\tme\\tmerge\\src\\services\\core-api\\Core.Domain\\Core.Domain.csproj": {"projectPath": "C:\\Work\\tme\\tmerge\\src\\services\\core-api\\Core.Domain\\Core.Domain.csproj"}, "C:\\Work\\tme\\tmerge\\src\\services\\shared\\Shared.Application\\Shared.Application.csproj": {"projectPath": "C:\\Work\\tme\\tmerge\\src\\services\\shared\\Shared.Application\\Shared.Application.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"FluentValidation.DependencyInjectionExtensions": {"target": "Package", "version": "[11.11.0, )", "versionCentrallyManaged": true}, "MediatR": {"target": "Package", "version": "[12.4.1, )", "versionCentrallyManaged": true}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[9.0.3, )", "versionCentrallyManaged": true}, "Refit": {"target": "Package", "version": "[8.0.0, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"FluentValidation.DependencyInjectionExtensions": "11.11.0", "MediatR": "12.4.1", "Microsoft.EntityFrameworkCore": "9.0.1", "Microsoft.EntityFrameworkCore.Design": "9.0.1", "Microsoft.EntityFrameworkCore.Tools": "9.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.1", "Microsoft.TeamFoundationServer.Client": "19.225.1", "Newtonsoft.Json": "13.0.3", "Npgsql.EntityFrameworkCore.PostgreSQL": "9.0.3", "OpenTelemetry.Exporter.OpenTelemetryProtocol": "1.12.0", "OpenTelemetry.Extensions.Hosting": "1.12.0", "OpenTelemetry.Instrumentation.AspNetCore": "1.12.0", "OpenTelemetry.Instrumentation.Http": "1.12.0", "OpenTelemetry.Instrumentation.Runtime": "1.12.0", "Refit": "8.0.0", "Refit.HttpClientFactory": "8.0.0", "Serilog": "4.2.0", "Serilog.AspNetCore": "9.0.0", "Serilog.Sinks.Grafana.Loki": "8.3.1", "Swashbuckle.AspNetCore": "7.2.0"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Work\\tme\\tmerge\\src\\services\\core-api\\Core.Domain\\Core.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Work\\tme\\tmerge\\src\\services\\core-api\\Core.Domain\\Core.Domain.csproj", "projectName": "Core.Domain", "projectPath": "C:\\Work\\tme\\tmerge\\src\\services\\core-api\\Core.Domain\\Core.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Work\\tme\\tmerge\\src\\services\\core-api\\Core.Domain\\obj\\", "projectStyle": "PackageReference", "centralPackageVersionsManagementEnabled": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Work\\tme\\tmerge\\src\\services\\shared\\Shared.Domain\\Shared.Domain.csproj": {"projectPath": "C:\\Work\\tme\\tmerge\\src\\services\\shared\\Shared.Domain\\Shared.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "centralPackageVersions": {"FluentValidation.DependencyInjectionExtensions": "11.11.0", "MediatR": "12.4.1", "Microsoft.EntityFrameworkCore": "9.0.1", "Microsoft.EntityFrameworkCore.Design": "9.0.1", "Microsoft.EntityFrameworkCore.Tools": "9.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.1", "Microsoft.TeamFoundationServer.Client": "19.225.1", "Newtonsoft.Json": "13.0.3", "Npgsql.EntityFrameworkCore.PostgreSQL": "9.0.3", "OpenTelemetry.Exporter.OpenTelemetryProtocol": "1.12.0", "OpenTelemetry.Extensions.Hosting": "1.12.0", "OpenTelemetry.Instrumentation.AspNetCore": "1.12.0", "OpenTelemetry.Instrumentation.Http": "1.12.0", "OpenTelemetry.Instrumentation.Runtime": "1.12.0", "Refit": "8.0.0", "Refit.HttpClientFactory": "8.0.0", "Serilog": "4.2.0", "Serilog.AspNetCore": "9.0.0", "Serilog.Sinks.Grafana.Loki": "8.3.1", "Swashbuckle.AspNetCore": "7.2.0"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Work\\tme\\tmerge\\src\\services\\core-api\\Core.Infrastructure\\Core.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Work\\tme\\tmerge\\src\\services\\core-api\\Core.Infrastructure\\Core.Infrastructure.csproj", "projectName": "Core.Infrastructure", "projectPath": "C:\\Work\\tme\\tmerge\\src\\services\\core-api\\Core.Infrastructure\\Core.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Work\\tme\\tmerge\\src\\services\\core-api\\Core.Infrastructure\\obj\\", "projectStyle": "PackageReference", "centralPackageVersionsManagementEnabled": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Work\\tme\\tmerge\\src\\services\\core-api\\Core.Application\\Core.Application.csproj": {"projectPath": "C:\\Work\\tme\\tmerge\\src\\services\\core-api\\Core.Application\\Core.Application.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.EntityFrameworkCore.Design": {"target": "Package", "version": "[9.0.1, )", "versionCentrallyManaged": true}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[9.0.3, )", "versionCentrallyManaged": true}, "Refit.HttpClientFactory": {"target": "Package", "version": "[8.0.0, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"FluentValidation.DependencyInjectionExtensions": "11.11.0", "MediatR": "12.4.1", "Microsoft.EntityFrameworkCore": "9.0.1", "Microsoft.EntityFrameworkCore.Design": "9.0.1", "Microsoft.EntityFrameworkCore.Tools": "9.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.1", "Microsoft.TeamFoundationServer.Client": "19.225.1", "Newtonsoft.Json": "13.0.3", "Npgsql.EntityFrameworkCore.PostgreSQL": "9.0.3", "OpenTelemetry.Exporter.OpenTelemetryProtocol": "1.12.0", "OpenTelemetry.Extensions.Hosting": "1.12.0", "OpenTelemetry.Instrumentation.AspNetCore": "1.12.0", "OpenTelemetry.Instrumentation.Http": "1.12.0", "OpenTelemetry.Instrumentation.Runtime": "1.12.0", "Refit": "8.0.0", "Refit.HttpClientFactory": "8.0.0", "Serilog": "4.2.0", "Serilog.AspNetCore": "9.0.0", "Serilog.Sinks.Grafana.Loki": "8.3.1", "Swashbuckle.AspNetCore": "7.2.0"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Work\\tme\\tmerge\\src\\services\\shared\\Shared.Application\\Shared.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Work\\tme\\tmerge\\src\\services\\shared\\Shared.Application\\Shared.Application.csproj", "projectName": "Shared.Application", "projectPath": "C:\\Work\\tme\\tmerge\\src\\services\\shared\\Shared.Application\\Shared.Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Work\\tme\\tmerge\\src\\services\\shared\\Shared.Application\\obj\\", "projectStyle": "PackageReference", "centralPackageVersionsManagementEnabled": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"FluentValidation.DependencyInjectionExtensions": {"target": "Package", "version": "[11.11.0, )", "versionCentrallyManaged": true}, "MediatR": {"target": "Package", "version": "[12.4.1, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"FluentValidation.DependencyInjectionExtensions": "11.11.0", "MediatR": "12.4.1", "Microsoft.EntityFrameworkCore": "9.0.1", "Microsoft.EntityFrameworkCore.Design": "9.0.1", "Microsoft.EntityFrameworkCore.Tools": "9.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.1", "Microsoft.TeamFoundationServer.Client": "19.225.1", "Newtonsoft.Json": "13.0.3", "Npgsql.EntityFrameworkCore.PostgreSQL": "9.0.3", "OpenTelemetry.Exporter.OpenTelemetryProtocol": "1.12.0", "OpenTelemetry.Extensions.Hosting": "1.12.0", "OpenTelemetry.Instrumentation.AspNetCore": "1.12.0", "OpenTelemetry.Instrumentation.Http": "1.12.0", "OpenTelemetry.Instrumentation.Runtime": "1.12.0", "Refit": "8.0.0", "Refit.HttpClientFactory": "8.0.0", "Serilog": "4.2.0", "Serilog.AspNetCore": "9.0.0", "Serilog.Sinks.Grafana.Loki": "8.3.1", "Swashbuckle.AspNetCore": "7.2.0"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Work\\tme\\tmerge\\src\\services\\shared\\Shared.Domain\\Shared.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Work\\tme\\tmerge\\src\\services\\shared\\Shared.Domain\\Shared.Domain.csproj", "projectName": "Shared.Domain", "projectPath": "C:\\Work\\tme\\tmerge\\src\\services\\shared\\Shared.Domain\\Shared.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Work\\tme\\tmerge\\src\\services\\shared\\Shared.Domain\\obj\\", "projectStyle": "PackageReference", "centralPackageVersionsManagementEnabled": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "centralPackageVersions": {"FluentValidation.DependencyInjectionExtensions": "11.11.0", "MediatR": "12.4.1", "Microsoft.EntityFrameworkCore": "9.0.1", "Microsoft.EntityFrameworkCore.Design": "9.0.1", "Microsoft.EntityFrameworkCore.Tools": "9.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.1", "Microsoft.TeamFoundationServer.Client": "19.225.1", "Newtonsoft.Json": "13.0.3", "Npgsql.EntityFrameworkCore.PostgreSQL": "9.0.3", "OpenTelemetry.Exporter.OpenTelemetryProtocol": "1.12.0", "OpenTelemetry.Extensions.Hosting": "1.12.0", "OpenTelemetry.Instrumentation.AspNetCore": "1.12.0", "OpenTelemetry.Instrumentation.Http": "1.12.0", "OpenTelemetry.Instrumentation.Runtime": "1.12.0", "Refit": "8.0.0", "Refit.HttpClientFactory": "8.0.0", "Serilog": "4.2.0", "Serilog.AspNetCore": "9.0.0", "Serilog.Sinks.Grafana.Loki": "8.3.1", "Swashbuckle.AspNetCore": "7.2.0"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}}