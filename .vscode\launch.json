{"version": "0.2.0", "configurations": [{"name": "Attach to .NET Docker", "type": "coreclr", "request": "attach", "processId": "${command:pickRemoteProcess}", "pipeTransport": {"pipeProgram": "docker", "pipeArgs": ["exec", "-i", "tmerge-dev-core-api-1"], "debuggerPath": "/usr/share/dotnet/vsdbg/vsdbg", "pipeCwd": "${workspaceRoot}", "quoteArgs": false}, "sourceFileMap": {"/app/Core.Api": "${workspaceRoot}/src/services/core-api/Core.Api", "/app": "${workspaceRoot}/src/services/core-api"}, "justMyCode": true, "logging": {"moduleLoad": false, "programOutput": true}}]}