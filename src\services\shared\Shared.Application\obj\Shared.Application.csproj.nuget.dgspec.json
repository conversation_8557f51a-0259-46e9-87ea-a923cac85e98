{"format": 1, "restore": {"C:\\Work\\tme\\tmerge\\src\\services\\shared\\Shared.Application\\Shared.Application.csproj": {}}, "projects": {"C:\\Work\\tme\\tmerge\\src\\services\\shared\\Shared.Application\\Shared.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Work\\tme\\tmerge\\src\\services\\shared\\Shared.Application\\Shared.Application.csproj", "projectName": "Shared.Application", "projectPath": "C:\\Work\\tme\\tmerge\\src\\services\\shared\\Shared.Application\\Shared.Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Work\\tme\\tmerge\\src\\services\\shared\\Shared.Application\\obj\\", "projectStyle": "PackageReference", "centralPackageVersionsManagementEnabled": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"FluentValidation.DependencyInjectionExtensions": {"target": "Package", "version": "[11.11.0, )", "versionCentrallyManaged": true}, "MediatR": {"target": "Package", "version": "[12.4.1, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"FluentValidation.DependencyInjectionExtensions": "11.11.0", "MediatR": "12.4.1", "Microsoft.EntityFrameworkCore": "9.0.1", "Microsoft.EntityFrameworkCore.Design": "9.0.1", "Microsoft.EntityFrameworkCore.Tools": "9.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.1", "Microsoft.TeamFoundationServer.Client": "19.225.1", "Newtonsoft.Json": "13.0.3", "Npgsql.EntityFrameworkCore.PostgreSQL": "9.0.3", "OpenTelemetry.Exporter.OpenTelemetryProtocol": "1.12.0", "OpenTelemetry.Extensions.Hosting": "1.12.0", "OpenTelemetry.Instrumentation.AspNetCore": "1.12.0", "OpenTelemetry.Instrumentation.Http": "1.12.0", "OpenTelemetry.Instrumentation.Runtime": "1.12.0", "Refit": "8.0.0", "Refit.HttpClientFactory": "8.0.0", "Serilog": "4.2.0", "Serilog.AspNetCore": "9.0.0", "Serilog.Sinks.Grafana.Loki": "8.3.1", "Swashbuckle.AspNetCore": "7.2.0"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}}