using Core.Application.Common.Interfaces;
using Core.Application.External.AdoApi;
using Core.Domain.ConnectionTypes;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Refit;

namespace Core.Infrastructure;

public static class DependencyInjection
{
	public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration) =>
		services
			.AddDatabase(configuration)
			.AddRefitClients(configuration);

	private static IServiceCollection AddDatabase(this IServiceCollection services, IConfiguration configuration)
	{
		//TODO: get from env variable instead
		string? connectionString = configuration.GetConnectionString("Database");

		services.AddDbContext<ApplicationDbContext>(
			options =>
			{
				options
					.UseNpgsql(connectionString, npgsqlOptions =>
						npgsqlOptions.MigrationsHistoryTable(HistoryRepository.DefaultTableName, Schemas.Default))
					.UseSeeding(SeedData);
			});

		services.AddScoped<IApplicationDbContext>(provider => provider.GetRequiredService<ApplicationDbContext>());

		return services;
	}

	public static IServiceCollection AddRefitClients(this IServiceCollection services, IConfiguration configuration)
	{
		services.AddRefitClient<IAdoApiConnectionsClient>()
			.ConfigureHttpClient(c =>
			{
				// TODO: from env variable
				c.BaseAddress = new Uri("http://ado-api:8082");
			});

		return services;
	}

	private readonly static Action<DbContext, bool> SeedData = (context, _) =>
	{
		if (!context.Set<ConnectionType>().Any())
		{
			context.Set<ConnectionType>().AddRange([
				new ConnectionType { Id = Guid.Parse("6bdd3139-a71d-402c-a54a-4f9175f89797"), Name = "Azure DevOps Services", ServiceName = "ado" },
				new ConnectionType { Id = Guid.Parse("15a7c796-7f5e-4489-9301-91fb6128aa53"), Name = "Azure DevOps Server 2022", ServiceName = "ado" },
			]);

			context.SaveChanges();
		}
	};
}
