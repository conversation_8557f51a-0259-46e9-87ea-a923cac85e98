using Core.Application.External.Interfaces;

namespace Core.Application.External;

public class ExternalServiceResolver : IExternalServiceResolver
{
    private readonly IDictionary<string, IProviderHandler> _handlers;

    public ExternalServiceResolver(IEnumerable<IProviderHandler> handlers)
    {
        _handlers = handlers.ToDictionary(h => h.ServiceName.ToLowerInvariant(), h => h);
    }

    public IProviderHandler GetHandler(string serviceName)
    {
        if (_handlers.TryGetValue(serviceName.ToLowerInvariant(), out var handler))
            return handler;
        throw new NotSupportedException($"Service '{serviceName}' is not supported.");
    }
}
