name: "tmerge-prod"
services:
  traefik:
    image: traefik:v3.0
    command:
      - "--configfile=/etc/traefik/traefik.yml"
    ports:
      - "80:80"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ../../src/traefik:/etc/traefik
    depends_on:
      - web
      - grafana

  web:
    image: tmerge-prod/web
    build:
      context: ../../src/web
    environment:
      NUXT_SESSION_PASSWORD: "a3ef07459d934d21a1a63402e9e8cfb3"
      NUXT_OAUTH_KEYCLOAK_CLIENT_ID: "nuxt-app"
      NUXT_OAUTH_KEYCLOAK_CLIENT_SECRET: "kc9LGZjbQ3Bf5wCXnCrpVBB6AYTXyBaK"
      NUXT_OAUTH_KEYCLOAK_SERVER_URL: "http://keycloak.localhost"
      NUXT_OAUTH_KEYCLOAK_SERVER_URL_INTERNAL: "http://keycloak:8080"
      NUXT_OAUTH_KEYCLOAK_REALM: "tmerge"
      NITRO_LOG_LEVEL: "info"
      DEBUG: "*"
      CORE_API_URL: "http://core-api:8081"
      ADO_API_URL: "http://ado-api:8082"
      BASE_URL: "http://tmerge.localhost"
    extra_hosts:
      - "keycloak.localhost:host-gateway"
    depends_on:
      - core-api
      - keycloak
