﻿<Project Sdk="Microsoft.NET.Sdk.Web">
	<ItemGroup>
		<PackageReference Include="OpenTelemetry.Extensions.Hosting" />
		<PackageReference Include="OpenTelemetry.Exporter.OpenTelemetryProtocol" />
		<PackageReference Include="OpenTelemetry.Instrumentation.AspNetCore" />
		<PackageReference Include="OpenTelemetry.Instrumentation.Http" />
		<PackageReference Include="OpenTelemetry.Instrumentation.Runtime" />
		<PackageReference Include="Swashbuckle.AspNetCore" />
		<PackageReference Include="Serilog.AspNetCore" />
		<PackageReference Include="Serilog.Sinks.Grafana.Loki" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Ado.Infrastructure\Ado.Infrastructure.csproj" />
	</ItemGroup>
</Project>
