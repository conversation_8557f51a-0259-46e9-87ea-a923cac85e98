<script setup lang="ts">
	import { getConnectionTypeImage, hasConnectionTypeImage } from "../../utils/connection-images";

	useHead({
		title: "tmerge - Connection Details"
	});

	// Get the connection ID from the route
	const route = useRoute();
	const { formatDate } = useDateFormatting();
	const connectionId = route.params.id as string;

	// Validate the connection ID
	if (!connectionId || typeof connectionId !== "string") {
		throw createError({
			statusCode: 400,
			statusMessage: "Invalid connection ID"
		});
	}

	const { data: connectionDetails, error, pending: isLoading, refresh: refreshConnectionDetails } = await useFetch<ConnectionDetails>(`/api/core?url=connections/${connectionId}`);

	// Computed properties for display
	const connectionImage = computed(() => {
		if (!connectionDetails.value) return null;
		return getConnectionTypeImage(connectionDetails.value.connectionType);
	});

	const hasCustomImage = computed(() => {
		if (!connectionDetails.value) return false;
		return hasConnectionTypeImage(connectionDetails.value.connectionType);
	});

	// Track if image loading failed
	const imageLoadFailed = ref(false);
	const shouldShowImage = computed(() => hasCustomImage.value && !imageLoadFailed.value);

	// Handle image load error
	function handleImageError() {
		imageLoadFailed.value = true;
	}
</script>

<template>
	<UContainer>
		<!-- Loading State -->
		<div v-if="isLoading" class="flex justify-center items-center min-h-[400px]">
			<div class="text-center">
				<UIcon name="i-heroicons-arrow-path" class="w-8 h-8 mx-auto mb-4 text-blue-500 animate-spin" />
				<p class="text-gray-600 dark:text-gray-400">Loading connection details...</p>
			</div>
		</div>

		<!-- Error State -->
		<div v-else-if="error" class="flex justify-center items-center min-h-[400px]">
			<div class="text-center">
				<UIcon name="i-heroicons-exclamation-triangle" class="w-12 h-12 mx-auto mb-4 text-red-500" />
				<h2 class="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">Failed to Load Connection</h2>
				<p class="text-gray-600 dark:text-gray-400 mb-4">{{ error.message }}</p>
				<UButton @click="() => refreshConnectionDetails()" variant="outline">
					<UIcon name="i-heroicons-arrow-path" class="w-4 h-4 mr-2" />
					Try Again
				</UButton>
			</div>
		</div>

		<!-- Connection Details -->
		<div v-else-if="connectionDetails" class="space-y-6">
			<!-- Header -->
			<div class="flex items-center justify-between">
				<div class="flex items-center gap-4">
					<UButton @click="navigateTo('/connections')" variant="ghost" size="sm" icon="i-heroicons-arrow-left"> Back to Connections </UButton>
				</div>
			</div>

			<!-- Main Content -->
			<UCard class="w-full">
				<div class="space-y-6">
					<!-- Header with connection name and type image/icon -->
					<div class="flex items-start gap-6">
						<div class="flex-shrink-0">
							<!-- Connection type image or fallback icon -->
							<div class="w-20 h-16 overflow-hidden bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm flex items-center justify-center p-3">
								<img
									v-if="shouldShowImage && connectionImage"
									:src="connectionImage.src"
									:alt="connectionImage.alt"
									class="w-full h-full object-contain filter drop-shadow-sm dark:brightness-90 dark:contrast-110"
									loading="lazy"
									@error="handleImageError"
								/>
								<UIcon v-else :name="connectionImage?.fallbackIcon || 'i-heroicons-link'" class="w-8 h-8 text-gray-500 dark:text-gray-400" />
							</div>
						</div>

						<div class="flex-1 min-w-0">
							<h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100 truncate">
								{{ connectionDetails.name }}
							</h1>
							<p class="text-lg text-gray-500 dark:text-gray-400 mt-1">
								{{ connectionDetails.connectionType.name }}
							</p>
							<div v-if="connectionDetails.description" class="text-gray-600 dark:text-gray-300 mt-2">
								{{ connectionDetails.description }}
							</div>
						</div>
					</div>

					<!-- Connection Information -->
					<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
						<!-- Basic Information -->
						<div class="space-y-4">
							<h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">Basic Information</h3>

							<div class="space-y-3">
								<div class="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-700">
									<span class="font-medium text-gray-600 dark:text-gray-400">Service:</span>
									<UBadge color="primary" variant="soft">
										{{ connectionDetails.connectionType.serviceName }}
									</UBadge>
								</div>

								<div class="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-700">
									<span class="font-medium text-gray-600 dark:text-gray-400">Status:</span>
									<UBadge :color="connectionDetails.connectionType.isEnabled ? 'success' : 'warning'" variant="soft">
										<UIcon :name="connectionDetails.connectionType.isEnabled ? 'i-heroicons-check-circle' : 'i-heroicons-pause-circle'" class="w-3 h-3 mr-1" />
										{{ connectionDetails.connectionType.isEnabled ? "Enabled" : "Disabled" }}
									</UBadge>
								</div>

								<ClientOnly>
									<div class="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-700">
										<span class="font-medium text-gray-600 dark:text-gray-400">Created:</span>
										<span class="text-sm text-gray-900 dark:text-gray-100">
											{{ formatDate(connectionDetails.createdAt) }}
										</span>
									</div>

									<div class="flex justify-between items-center py-2">
										<span class="font-medium text-gray-600 dark:text-gray-400">Last Updated:</span>
										<span class="text-sm text-gray-900 dark:text-gray-100">
											{{ formatDate(connectionDetails.lastUpdatedAt) }}
										</span>
									</div>
								</ClientOnly>
							</div>
						</div>

						<!-- Provider-Specific Details -->
						<div class="space-y-4">
							<h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">Provider Details</h3>

							<!-- Generic provider details display -->
							<ConnectionProviderDetails :connection-type="connectionDetails.connectionType" :provider-details="connectionDetails.providerDetails" />
						</div>
					</div>
				</div>
			</UCard>
		</div>
	</UContainer>
</template>
