using Ado.Application.AzureDevOpsClients.Validation.Queries;
using Ado.Application.Common.Interfaces;
using Ado.Domain.Entities;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Ado.Application.Connections.Commands.CreateConnection;

public record CreateConnectionCommand : IRequest<Connection>
{
	public required string Name { get; init; }
	public Guid ConnectionTypeId { get; set; }
	public Guid CoreConnectionId { get; set; }
	public required CreateConnectionPayload Payload { get; set; }
}

public class CreateConnectionCommandHandler(ILogger<CreateConnectionCommandHandler> _logger, IApplicationDbContext _context, ISender _mediator) : IRequestHandler<CreateConnectionCommand, Connection>
{
	public async Task<Connection> Handle(CreateConnectionCommand request, CancellationToken cancellationToken)
	{
		await _mediator.Send(new ValidateAdoConnectionQuery { AdoInstanceUrl = request.Payload.AdoInstanceUrl, Pat = request.Payload.Pat }, cancellationToken);

		// Create the Connection entity with all required properties
		var entity = new Connection
		{
			Name = request.Name,
			CoreConnectionId = request.CoreConnectionId,
			AdoInstanceUrl = request.Payload.AdoInstanceUrl,
			Pat = request.Payload.Pat
		};

		_context.Connections.Add(entity);

		await _context.SaveChangesAsync(cancellationToken);

		_logger.LogInformation("ADO Connection created successfully with ID: {ConnectionId}", entity.Id);

		return entity;
	}
}
