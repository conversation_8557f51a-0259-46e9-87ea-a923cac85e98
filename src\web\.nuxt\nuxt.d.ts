// Generated by nuxi
/// <reference types="@vueuse/nuxt" />
/// <reference types="@pinia/nuxt" />
/// <reference types="@nuxt/telemetry" />
/// <reference types="nuxt-auth-utils" />
/// <reference types="@nuxt/ui" />
/// <reference types="@nuxt/devtools" />
/// <reference path="types/builder-env.d.ts" />
/// <reference types="nuxt" />
/// <reference path="types/app-defaults.d.ts" />
/// <reference path="types/plugins.d.ts" />
/// <reference path="types/build.d.ts" />
/// <reference path="types/schema.d.ts" />
/// <reference path="types/app.config.d.ts" />
/// <reference path="types/ui.d.ts" />
/// <reference path="../node_modules/.pnpm/@nuxt+ui@3.1.2_@babel+parse_f07273e8df9b8a2f3a26358e2de67989/node_modules/@nuxt/ui/dist/runtime/types/app.config.d.ts" />
/// <reference types="@pinia/nuxt" />
/// <reference types="vue-router" />
/// <reference path="types/middleware.d.ts" />
/// <reference path="types/nitro-middleware.d.ts" />
/// <reference path="types/layouts.d.ts" />
/// <reference path="components.d.ts" />
/// <reference path="imports.d.ts" />
/// <reference path="types/imports.d.ts" />
/// <reference path="schema/nuxt.schema.d.ts" />
/// <reference path="types/nitro.d.ts" />

export {}
