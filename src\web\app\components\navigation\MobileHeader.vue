<script setup lang="ts">
	import type { NavigationMenuItem } from "@nuxt/ui";

	const isOpen = ref<boolean>();

	const { links } = await useNavigationLinks();

	const processedLinks = computed(() => {
		const closableLinks = links.value.map((link) => {
			// the collapsible parent links shouldn't close the slideover - only toggle the collapsed elements
			if (!link.defaultOpen) {
				link.onSelect = () => (isOpen.value = false);
			}

			link.children?.forEach((child) => {
				child.onSelect = () => (isOpen.value = false);
			});

			return link;
		});

		return closableLinks;
	});
</script>

<template>
	<header class="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-800 bg-background">
		<USlideover v-model:open="isOpen" side="left" :title="`tmerge`">
			<UButton icon="i-heroicons-bars-3" variant="ghost" @click="isOpen = true" />

			<template #body>
				<div class="flex flex-col flex-1 p-4">
					<UNavigationMenu orientation="vertical" :items="processedLinks" class="flex-1">
						<template #user-leading="{ item }: { item: NavigationMenuItem }">
							<UAvatar size="sm" icon="i-heroicons-user" :src="item.value || ''" />
						</template>
					</UNavigationMenu>
				</div>
			</template>
		</USlideover>

		<NavigationProfileSettings :align="'start'" side="top" />
	</header>
</template>
