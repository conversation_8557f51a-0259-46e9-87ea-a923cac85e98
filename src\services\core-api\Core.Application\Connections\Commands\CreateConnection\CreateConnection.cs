using Core.Application.Common.Interfaces;
using Core.Application.External.Interfaces;
using Core.Domain.Connection;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Application.Exceptions;

namespace Core.Application.Connections.Commands.CreateConnection;

public record CreateConnectionCommand : IRequest<Connection>
{
	public required string Name { get; init; }
	public Guid ConnectionTypeId { get; set; }
	public string? Description { get; set; }
	public dynamic? Payload { get; set; }
}

public class CreateConnectionCommandHandler(ILogger<CreateConnectionCommandHandler> _logger, IApplicationDbContext _context, IExternalServiceResolver _serviceResolver) : IRequestHandler<CreateConnectionCommand, Connection>
{
	public async Task<Connection> Handle(CreateConnectionCommand request, CancellationToken cancellationToken)
	{
		var connectionType = await _context.ConnectionTypes.FindAsync([request.ConnectionTypeId], cancellationToken) ?? throw new NotFoundException($"ConnectionType with ID '{request.ConnectionTypeId}' was not found.");

		// Create the Connection entity with all required properties
		var entity = new Connection
		{
			Name = request.Name,
			ConnectionType = connectionType,
			Description = request.Description
		};

		_context.Connections.Add(entity);

		var handler = _serviceResolver.GetHandler(entity.ConnectionType.ServiceName);
		var requestDto = new CreateConnectionExternalDto
		{
			Name = entity.Name,
			CoreConnectionId = entity.Id,
			ConnectionTypeId = entity.ConnectionType.Id,
			Payload = request.Payload
		};

		if (handler is IApiConnectionsClient client)
		{
			await client.CreateConnectionAsync(requestDto);
		}
		// else: skip external creation for unknown handler

		await _context.SaveChangesAsync(cancellationToken);

		_logger.LogInformation("Connection created successfully with ID: {ConnectionId}", entity.Id);

		return entity;
	}
}
