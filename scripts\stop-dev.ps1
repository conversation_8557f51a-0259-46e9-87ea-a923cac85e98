# Stop development environment
Write-Host "Stopping tmerge development environment..." -ForegroundColor Green
# Navigate to project root
Set-Location $PSScriptRoot\..

# Stop Docker Compose
docker compose -p tmerge-dev down

if ($LASTEXITCODE -eq 0) {
	Write-Host "SUCCESS: Development environment stopped successfully!" -ForegroundColor Green
}
else {
	Write-Host "ERROR: Failed to stop development environment" -ForegroundColor Red
	Read-Host ""
	exit 1
}

Set-Location $PSScriptRoot
Read-Host "Execution complete. Press Enter to exit..."
