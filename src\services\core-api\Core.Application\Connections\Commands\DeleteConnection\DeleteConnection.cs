using Core.Application.Common.Interfaces;
using Core.Domain.Connection;
using MediatR;
using Shared.Application.Exceptions;

namespace Core.Application.Connections.Commands.DeleteConnection;

public record DeleteConnectionCommand : IRequest<Connection>
{
	public Guid ConnectionId { get; set; }
}

public class DeleteConnectionCommandHandler(IApplicationDbContext _context) : IRequestHandler<DeleteConnectionCommand, Connection>
{
	public async Task<Connection> Handle(DeleteConnectionCommand request, CancellationToken cancellationToken)
	{
		var connection = await _context.Connections.FindAsync([request.ConnectionId], cancellationToken) ?? throw new NotFoundException($"Connection with ID '{request.ConnectionId}' was not found.");

		connection.IsDeleted = !connection.IsDeleted;

		// call corresponding service's connection entity to delete it too

		await _context.SaveChangesAsync(cancellationToken);

		return connection;
	}
}
