export default defineEventHandler(async (event): Promise<unknown> => {
	try {
		const { url } = getQuery(event);
		const data = await $fetch(`${process.env.CORE_API_URL}/api/${url}`);

		return data;
	} catch (error: any) {
		console.error("Error details:", {
			statusCode: error.statusCode,
			status: error.status,
			data: error.data,
			response: error.response,
			message: error.message
		});

		// Preserve the original status code and error details from the backend
		const statusCode = error.statusCode || error.status || 500;
		const errorData = error.data || error.response?._data;

		// If we have structured error data, pass it through
		if (errorData && typeof errorData === 'object') {
			throw createError({
				statusCode,
				data: errorData
			});
		}

		// Fallback for simple errors
		throw createError({
			statusCode,
			statusMessage: error.message || 'API request failed'
		});
	}
});
